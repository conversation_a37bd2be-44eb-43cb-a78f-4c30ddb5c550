{"id": "b01afeeb-eaf0-4ffa-a129-b46cefc175dc", "name": "录制脚本_20250526_024956", "description": "自动录制的脚本", "actions": [{"MouseMove": {"x": 769, "y": 359}}, {"MouseMove": {"x": 769, "y": 353}}, {"MouseMove": {"x": 769, "y": 345}}, {"MouseMove": {"x": 770, "y": 335}}, {"MouseMove": {"x": 771, "y": 323}}, {"MouseMove": {"x": 772, "y": 310}}, {"MouseMove": {"x": 773, "y": 294}}, {"MouseMove": {"x": 776, "y": 240}}, {"MouseMove": {"x": 778, "y": 200}}, {"MouseMove": {"x": 779, "y": 153}}, {"MouseMove": {"x": 781, "y": 101}}, {"MouseMove": {"x": 782, "y": 39}}, {"MouseMove": {"x": 783, "y": -22}}, {"MouseMove": {"x": 785, "y": -87}}, {"MouseMove": {"x": 785, "y": -144}}, {"MouseMove": {"x": 785, "y": -169}}, {"MouseMove": {"x": 785, "y": -216}}, {"MouseMove": {"x": 785, "y": -258}}, {"MouseMove": {"x": 785, "y": -274}}, {"MouseMove": {"x": 785, "y": -302}}, {"MouseMove": {"x": 784, "y": -310}}, {"MouseMove": {"x": 784, "y": -316}}, {"MouseMove": {"x": 782, "y": -327}}, {"MouseMove": {"x": 783, "y": -328}}, {"MouseMove": {"x": 787, "y": -332}}, {"MouseMove": {"x": 791, "y": -336}}, {"MouseMove": {"x": 796, "y": -340}}, {"MouseMove": {"x": 801, "y": -344}}, {"MouseMove": {"x": 809, "y": -352}}, {"MouseMove": {"x": 818, "y": -360}}, {"MouseMove": {"x": 828, "y": -369}}, {"MouseMove": {"x": 836, "y": -376}}, {"MouseMove": {"x": 845, "y": -385}}, {"MouseMove": {"x": 849, "y": -388}}, {"MouseMove": {"x": 857, "y": -394}}, {"MouseMove": {"x": 864, "y": -400}}, {"MouseMove": {"x": 870, "y": -406}}, {"MouseMove": {"x": 875, "y": -411}}, {"MouseMove": {"x": 880, "y": -415}}, {"MouseMove": {"x": 884, "y": -418}}, {"MouseMove": {"x": 885, "y": -420}}, {"MouseMove": {"x": 888, "y": -422}}, {"MouseMove": {"x": 890, "y": -425}}, {"MouseMove": {"x": 893, "y": -427}}, {"MouseMove": {"x": 894, "y": -429}}, {"MouseMove": {"x": 896, "y": -430}}, {"MouseMove": {"x": 897, "y": -431}}, {"MouseMove": {"x": 898, "y": -433}}, {"MouseMove": {"x": 898, "y": -434}}, {"MouseMove": {"x": 899, "y": -435}}, {"MouseMove": {"x": 900, "y": -436}}, {"MouseMove": {"x": 901, "y": -438}}, {"MouseMove": {"x": 902, "y": -440}}, {"MouseMove": {"x": 902, "y": -443}}, {"MouseMove": {"x": 903, "y": -445}}, {"MouseMove": {"x": 904, "y": -448}}, {"MouseMove": {"x": 904, "y": -451}}, {"MouseMove": {"x": 905, "y": -453}}, {"MouseMove": {"x": 906, "y": -456}}, {"MouseMove": {"x": 906, "y": -458}}, {"MouseMove": {"x": 907, "y": -460}}, {"MouseMove": {"x": 907, "y": -462}}, {"MouseMove": {"x": 907, "y": -463}}, {"MouseMove": {"x": 908, "y": -464}}, {"MouseMove": {"x": 908, "y": -465}}, {"MouseMove": {"x": 908, "y": -465}}, {"MouseMove": {"x": 908, "y": -465}}, {"MouseMove": {"x": 906, "y": -466}}, {"MouseMove": {"x": 903, "y": -466}}, {"MouseMove": {"x": 898, "y": -466}}, {"MouseMove": {"x": 893, "y": -466}}, {"MouseMove": {"x": 881, "y": -466}}, {"MouseMove": {"x": 872, "y": -466}}, {"MouseMove": {"x": 868, "y": -466}}, {"MouseMove": {"x": 860, "y": -465}}, {"MouseMove": {"x": 853, "y": -463}}, {"MouseMove": {"x": 848, "y": -460}}, {"MouseMove": {"x": 843, "y": -458}}, {"MouseMove": {"x": 840, "y": -454}}, {"MouseMove": {"x": 836, "y": -450}}, {"MouseMove": {"x": 833, "y": -446}}, {"MouseMove": {"x": 829, "y": -438}}, {"MouseMove": {"x": 825, "y": -430}}, {"MouseMove": {"x": 821, "y": -420}}, {"MouseMove": {"x": 816, "y": -408}}, {"MouseMove": {"x": 810, "y": -396}}, {"MouseMove": {"x": 799, "y": -371}}, {"MouseMove": {"x": 793, "y": -355}}, {"MouseMove": {"x": 789, "y": -345}}, {"MouseMove": {"x": 776, "y": -313}}, {"MouseMove": {"x": 768, "y": -290}}, {"MouseMove": {"x": 761, "y": -268}}, {"MouseMove": {"x": 752, "y": -244}}, {"MouseMove": {"x": 744, "y": -220}}, {"MouseMove": {"x": 741, "y": -209}}, {"MouseMove": {"x": 734, "y": -187}}, {"MouseMove": {"x": 726, "y": -165}}, {"MouseMove": {"x": 720, "y": -144}}, {"MouseMove": {"x": 715, "y": -125}}, {"MouseMove": {"x": 710, "y": -110}}, {"MouseMove": {"x": 708, "y": -104}}, {"MouseMove": {"x": 705, "y": -93}}, {"MouseMove": {"x": 704, "y": -90}}, {"MouseMove": {"x": 702, "y": -83}}, {"MouseMove": {"x": 698, "y": -76}}, {"MouseMove": {"x": 693, "y": -73}}, {"MouseMove": {"x": 693, "y": -73}}, {"MouseMove": {"x": 693, "y": -62}}, {"MouseMove": {"x": 692, "y": -50}}, {"MouseMove": {"x": 691, "y": -35}}, {"MouseMove": {"x": 687, "y": -17}}, {"MouseMove": {"x": 683, "y": 1}}, {"MouseMove": {"x": 671, "y": 44}}, {"MouseMove": {"x": 658, "y": 95}}, {"MouseMove": {"x": 641, "y": 155}}, {"MouseMove": {"x": 624, "y": 221}}, {"MouseMove": {"x": 608, "y": 287}}, {"MouseMove": {"x": 591, "y": 355}}, {"MouseMove": {"x": 584, "y": 382}}, {"MouseMove": {"x": 569, "y": 438}}, {"MouseMove": {"x": 557, "y": 481}}, {"MouseMove": {"x": 553, "y": 494}}, {"MouseMove": {"x": 542, "y": 523}}, {"MouseMove": {"x": 539, "y": 531}}, {"MouseMove": {"x": 532, "y": 546}}, {"MouseMove": {"x": 519, "y": 564}}, {"MouseMove": {"x": 524, "y": 564}}, {"MouseMove": {"x": 528, "y": 562}}, {"MouseMove": {"x": 533, "y": 561}}, {"MouseMove": {"x": 541, "y": 560}}, {"MouseMove": {"x": 549, "y": 559}}, {"MouseMove": {"x": 557, "y": 559}}, {"MouseMove": {"x": 565, "y": 558}}, {"MouseMove": {"x": 575, "y": 558}}, {"MouseMove": {"x": 583, "y": 557}}, {"MouseMove": {"x": 602, "y": 555}}, {"MouseMove": {"x": 621, "y": 554}}, {"MouseMove": {"x": 639, "y": 551}}, {"MouseMove": {"x": 658, "y": 548}}, {"MouseMove": {"x": 675, "y": 544}}, {"MouseMove": {"x": 689, "y": 541}}, {"MouseMove": {"x": 703, "y": 537}}, {"MouseMove": {"x": 714, "y": 532}}, {"MouseMove": {"x": 718, "y": 529}}, {"MouseMove": {"x": 733, "y": 520}}, {"MouseMove": {"x": 738, "y": 515}}, {"MouseMove": {"x": 741, "y": 508}}, {"MouseMove": {"x": 741, "y": 501}}, {"MouseMove": {"x": 741, "y": 500}}, {"MouseMove": {"x": 743, "y": 497}}, {"MouseMove": {"x": 746, "y": 494}}, {"MouseMove": {"x": 748, "y": 491}}, {"MouseMove": {"x": 751, "y": 488}}, {"MouseMove": {"x": 753, "y": 485}}, {"MouseMove": {"x": 758, "y": 480}}, {"MouseMove": {"x": 762, "y": 475}}, {"MouseMove": {"x": 766, "y": 470}}, {"MouseMove": {"x": 770, "y": 466}}, {"MouseMove": {"x": 773, "y": 463}}, {"MouseMove": {"x": 774, "y": 462}}, {"MouseMove": {"x": 777, "y": 459}}, {"MouseMove": {"x": 779, "y": 456}}, {"MouseMove": {"x": 781, "y": 455}}, {"MouseMove": {"x": 782, "y": 453}}, {"MouseMove": {"x": 783, "y": 452}}, {"MouseMove": {"x": 785, "y": 451}}, {"MouseMove": {"x": 786, "y": 450}}, {"MouseMove": {"x": 787, "y": 450}}, {"MouseMove": {"x": 788, "y": 449}}, {"MouseMove": {"x": 789, "y": 449}}, {"MouseMove": {"x": 789, "y": 448}}, {"MouseMove": {"x": 790, "y": 447}}, {"MouseMove": {"x": 791, "y": 447}}, {"MouseMove": {"x": 792, "y": 447}}, {"MouseMove": {"x": 792, "y": 446}}, {"MouseMove": {"x": 793, "y": 446}}, {"MouseMove": {"x": 793, "y": 445}}, {"MouseMove": {"x": 794, "y": 445}}, {"MouseMove": {"x": 794, "y": 445}}, {"MouseMove": {"x": 794, "y": 445}}, {"MouseMove": {"x": 794, "y": 444}}, {"MouseMove": {"x": 794, "y": 444}}, {"MouseMove": {"x": 795, "y": 444}}, {"MouseMove": {"x": 795, "y": 444}}, {"MouseClick": {"button": "Left", "x": 0, "y": 0}}], "created_at": "2025-05-26T02:49:56.686101Z", "modified_at": "2025-05-26T02:49:56.686500Z", "tags": []}