{"id": "98c2e6ee-bea5-4c81-9d93-3272777d7ce0", "name": "录制脚本_20250526_060028", "description": "自动录制的脚本", "actions": [{"MouseMove": {"x": 903, "y": 264}}, {"MouseMove": {"x": 902, "y": 269}}, {"MouseMove": {"x": 899, "y": 280}}, {"MouseMove": {"x": 895, "y": 293}}, {"MouseMove": {"x": 888, "y": 323}}, {"MouseMove": {"x": 881, "y": 353}}, {"MouseMove": {"x": 872, "y": 394}}, {"MouseMove": {"x": 863, "y": 441}}, {"MouseMove": {"x": 854, "y": 497}}, {"MouseMove": {"x": 843, "y": 562}}, {"MouseMove": {"x": 834, "y": 632}}, {"MouseMove": {"x": 823, "y": 707}}, {"MouseMove": {"x": 815, "y": 782}}, {"MouseMove": {"x": 806, "y": 858}}, {"MouseMove": {"x": 800, "y": 928}}, {"MouseMove": {"x": 798, "y": 956}}, {"MouseMove": {"x": 794, "y": 1010}}, {"MouseMove": {"x": 793, "y": 1028}}, {"MouseMove": {"x": 790, "y": 1062}}, {"MouseMove": {"x": 788, "y": 1070}}, {"MouseMove": {"x": 787, "y": 1076}}, {"MouseMove": {"x": 785, "y": 1089}}, {"MouseMove": {"x": 780, "y": 1104}}, {"MouseMove": {"x": 775, "y": 1105}}, {"MouseMove": {"x": 769, "y": 1108}}, {"MouseMove": {"x": 763, "y": 1111}}, {"MouseMove": {"x": 758, "y": 1113}}, {"MouseMove": {"x": 753, "y": 1115}}, {"MouseMove": {"x": 748, "y": 1117}}, {"MouseMove": {"x": 742, "y": 1119}}, {"MouseMove": {"x": 737, "y": 1119}}, {"MouseClick": {"button": "Left", "x": 733, "y": 1120}}, {"MouseMove": {"x": 1276, "y": 1112}}, {"MouseMove": {"x": 1268, "y": 1098}}, {"MouseMove": {"x": 1260, "y": 1083}}, {"MouseMove": {"x": 1250, "y": 1069}}, {"MouseMove": {"x": 1240, "y": 1052}}, {"MouseMove": {"x": 1227, "y": 1034}}, {"MouseMove": {"x": 1214, "y": 1014}}, {"MouseMove": {"x": 1187, "y": 973}}, {"MouseMove": {"x": 1159, "y": 929}}, {"MouseMove": {"x": 1130, "y": 885}}, {"MouseMove": {"x": 1117, "y": 865}}, {"MouseMove": {"x": 1090, "y": 826}}, {"MouseMove": {"x": 1064, "y": 789}}, {"MouseMove": {"x": 1040, "y": 756}}, {"MouseMove": {"x": 1018, "y": 724}}, {"MouseMove": {"x": 1011, "y": 713}}, {"MouseMove": {"x": 995, "y": 689}}, {"MouseMove": {"x": 990, "y": 683}}, {"MouseMove": {"x": 979, "y": 669}}, {"MouseMove": {"x": 972, "y": 659}}, {"MouseMove": {"x": 971, "y": 653}}, {"MouseMove": {"x": 969, "y": 644}}, {"MouseMove": {"x": 967, "y": 634}}, {"MouseMove": {"x": 964, "y": 623}}, {"MouseMove": {"x": 961, "y": 614}}, {"MouseMove": {"x": 959, "y": 603}}, {"MouseMove": {"x": 957, "y": 592}}, {"MouseMove": {"x": 955, "y": 581}}, {"MouseMove": {"x": 950, "y": 561}}, {"MouseMove": {"x": 945, "y": 542}}, {"MouseMove": {"x": 941, "y": 525}}, {"MouseMove": {"x": 938, "y": 510}}, {"MouseMove": {"x": 934, "y": 495}}, {"MouseMove": {"x": 931, "y": 482}}, {"MouseMove": {"x": 929, "y": 476}}, {"MouseMove": {"x": 927, "y": 465}}, {"MouseMove": {"x": 924, "y": 456}}, {"MouseMove": {"x": 922, "y": 449}}, {"MouseMove": {"x": 920, "y": 443}}, {"MouseMove": {"x": 918, "y": 437}}, {"MouseMove": {"x": 917, "y": 432}}, {"MouseMove": {"x": 914, "y": 423}}, {"MouseMove": {"x": 911, "y": 417}}, {"MouseMove": {"x": 910, "y": 412}}, {"MouseMove": {"x": 908, "y": 407}}, {"MouseMove": {"x": 907, "y": 402}}, {"MouseMove": {"x": 907, "y": 395}}, {"MouseMove": {"x": 907, "y": 387}}, {"MouseMove": {"x": 907, "y": 380}}, {"MouseMove": {"x": 906, "y": 371}}, {"MouseMove": {"x": 905, "y": 366}}, {"MouseMove": {"x": 904, "y": 360}}, {"MouseMove": {"x": 903, "y": 354}}, {"MouseMove": {"x": 902, "y": 348}}, {"MouseMove": {"x": 902, "y": 343}}, {"MouseMove": {"x": 902, "y": 338}}, {"MouseMove": {"x": 902, "y": 333}}, {"MouseClick": {"button": "Left", "x": 902, "y": 333}}], "created_at": "2025-05-26T06:00:28.935735Z", "modified_at": "2025-05-26T06:00:28.938128Z", "tags": []}