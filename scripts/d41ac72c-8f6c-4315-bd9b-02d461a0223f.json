{"id": "d41ac72c-8f6c-4315-bd9b-02d461a0223f", "name": "录制脚本_20250526_024636", "description": "自动录制的脚本", "actions": [{"MouseMove": {"x": 898, "y": 430}}, {"MouseMove": {"x": 898, "y": 429}}, {"MouseMove": {"x": 898, "y": 428}}, {"MouseMove": {"x": 898, "y": 427}}, {"MouseMove": {"x": 898, "y": 425}}, {"MouseMove": {"x": 898, "y": 422}}, {"MouseMove": {"x": 898, "y": 421}}, {"MouseMove": {"x": 898, "y": 418}}, {"MouseMove": {"x": 899, "y": 415}}, {"MouseMove": {"x": 900, "y": 412}}, {"MouseMove": {"x": 901, "y": 408}}, {"MouseMove": {"x": 904, "y": 401}}, {"MouseMove": {"x": 906, "y": 393}}, {"MouseMove": {"x": 910, "y": 385}}, {"MouseMove": {"x": 913, "y": 375}}, {"MouseMove": {"x": 917, "y": 363}}, {"MouseMove": {"x": 926, "y": 339}}, {"MouseMove": {"x": 931, "y": 320}}, {"MouseMove": {"x": 936, "y": 297}}, {"MouseMove": {"x": 940, "y": 272}}, {"MouseMove": {"x": 945, "y": 245}}, {"MouseMove": {"x": 949, "y": 217}}, {"MouseMove": {"x": 954, "y": 187}}, {"MouseMove": {"x": 958, "y": 156}}, {"MouseMove": {"x": 962, "y": 124}}, {"MouseMove": {"x": 965, "y": 96}}, {"MouseMove": {"x": 969, "y": 67}}, {"MouseMove": {"x": 971, "y": 39}}, {"MouseMove": {"x": 973, "y": 27}}, {"MouseMove": {"x": 974, "y": 2}}, {"MouseMove": {"x": 975, "y": -19}}, {"MouseMove": {"x": 977, "y": -39}}, {"MouseMove": {"x": 978, "y": -46}}, {"MouseMove": {"x": 978, "y": -50}}, {"MouseMove": {"x": 978, "y": -50}}, {"MouseMove": {"x": 978, "y": -53}}, {"MouseMove": {"x": 978, "y": -56}}, {"MouseMove": {"x": 978, "y": -59}}, {"MouseMove": {"x": 979, "y": -62}}, {"MouseMove": {"x": 980, "y": -66}}, {"MouseMove": {"x": 980, "y": -70}}, {"MouseMove": {"x": 981, "y": -77}}, {"MouseMove": {"x": 981, "y": -84}}, {"MouseMove": {"x": 982, "y": -98}}, {"MouseMove": {"x": 982, "y": -108}}, {"MouseMove": {"x": 982, "y": -119}}, {"MouseMove": {"x": 982, "y": -130}}, {"MouseMove": {"x": 978, "y": -140}}, {"MouseMove": {"x": 975, "y": -149}}, {"MouseMove": {"x": 973, "y": -154}}, {"MouseMove": {"x": 968, "y": -162}}, {"MouseMove": {"x": 964, "y": -169}}, {"MouseMove": {"x": 960, "y": -174}}, {"MouseMove": {"x": 956, "y": -178}}, {"MouseMove": {"x": 953, "y": -182}}, {"MouseMove": {"x": 949, "y": -183}}, {"MouseMove": {"x": 946, "y": -185}}, {"MouseMove": {"x": 942, "y": -186}}, {"MouseMove": {"x": 939, "y": -186}}, {"MouseMove": {"x": 935, "y": -186}}, {"MouseMove": {"x": 930, "y": -186}}, {"MouseMove": {"x": 926, "y": -186}}, {"MouseMove": {"x": 920, "y": -186}}, {"MouseMove": {"x": 916, "y": -186}}, {"MouseMove": {"x": 912, "y": -186}}, {"MouseMove": {"x": 907, "y": -186}}, {"MouseMove": {"x": 904, "y": -185}}, {"MouseMove": {"x": 900, "y": -184}}, {"MouseMove": {"x": 897, "y": -182}}, {"MouseMove": {"x": 894, "y": -182}}, {"MouseMove": {"x": 891, "y": -180}}, {"MouseMove": {"x": 888, "y": -179}}, {"MouseMove": {"x": 887, "y": -178}}, {"MouseMove": {"x": 883, "y": -177}}, {"MouseMove": {"x": 882, "y": -176}}, {"MouseMove": {"x": 880, "y": -175}}, {"MouseMove": {"x": 877, "y": -174}}, {"MouseMove": {"x": 874, "y": -173}}, {"MouseMove": {"x": 874, "y": -174}}, {"MouseMove": {"x": 874, "y": -179}}, {"MouseMove": {"x": 873, "y": -185}}, {"MouseMove": {"x": 872, "y": -194}}, {"MouseMove": {"x": 870, "y": -202}}, {"MouseMove": {"x": 866, "y": -221}}, {"MouseMove": {"x": 861, "y": -243}}, {"MouseMove": {"x": 853, "y": -267}}, {"MouseMove": {"x": 846, "y": -292}}, {"MouseMove": {"x": 836, "y": -320}}, {"MouseMove": {"x": 827, "y": -347}}, {"MouseMove": {"x": 818, "y": -374}}, {"MouseMove": {"x": 809, "y": -400}}, {"MouseMove": {"x": 805, "y": -413}}, {"MouseMove": {"x": 790, "y": -453}}, {"MouseMove": {"x": 786, "y": -463}}, {"MouseMove": {"x": 783, "y": -470}}, {"MouseMove": {"x": 777, "y": -484}}, {"MouseMove": {"x": 777, "y": -485}}, {"MouseMove": {"x": 777, "y": -490}}, {"MouseMove": {"x": 777, "y": -493}}, {"MouseMove": {"x": 777, "y": -497}}, {"MouseMove": {"x": 777, "y": -502}}, {"MouseMove": {"x": 777, "y": -507}}, {"MouseMove": {"x": 775, "y": -512}}, {"MouseMove": {"x": 773, "y": -516}}, {"MouseMove": {"x": 769, "y": -525}}, {"MouseMove": {"x": 764, "y": -534}}, {"MouseMove": {"x": 758, "y": -542}}, {"MouseMove": {"x": 753, "y": -551}}, {"MouseMove": {"x": 746, "y": -558}}, {"MouseMove": {"x": 740, "y": -565}}, {"MouseMove": {"x": 736, "y": -568}}, {"MouseMove": {"x": 728, "y": -574}}, {"MouseMove": {"x": 721, "y": -579}}, {"MouseMove": {"x": 716, "y": -582}}, {"MouseMove": {"x": 711, "y": -585}}, {"MouseMove": {"x": 708, "y": -585}}, {"MouseMove": {"x": 705, "y": -587}}, {"MouseMove": {"x": 702, "y": -587}}, {"MouseMove": {"x": 699, "y": -588}}, {"MouseMove": {"x": 698, "y": -588}}, {"MouseMove": {"x": 697, "y": -589}}, {"MouseMove": {"x": 696, "y": -589}}, {"MouseMove": {"x": 695, "y": -589}}, {"MouseMove": {"x": 695, "y": -589}}, {"MouseMove": {"x": 695, "y": -589}}, {"MouseMove": {"x": 695, "y": -589}}, {"MouseMove": {"x": 695, "y": -589}}, {"MouseClick": {"button": "Left", "x": 0, "y": 0}}, {"MouseMove": {"x": 921, "y": 114}}, {"MouseMove": {"x": 921, "y": 115}}, {"MouseMove": {"x": 928, "y": 127}}, {"MouseMove": {"x": 937, "y": 140}}, {"MouseMove": {"x": 948, "y": 153}}, {"MouseMove": {"x": 961, "y": 173}}, {"MouseMove": {"x": 975, "y": 190}}, {"MouseMove": {"x": 989, "y": 209}}, {"MouseMove": {"x": 1003, "y": 227}}, {"MouseMove": {"x": 1029, "y": 261}}, {"MouseMove": {"x": 1051, "y": 294}}, {"MouseMove": {"x": 1058, "y": 307}}, {"MouseMove": {"x": 1073, "y": 333}}, {"MouseMove": {"x": 1083, "y": 354}}, {"MouseMove": {"x": 1089, "y": 375}}, {"MouseMove": {"x": 1091, "y": 393}}, {"MouseMove": {"x": 1091, "y": 408}}, {"MouseMove": {"x": 1091, "y": 424}}, {"MouseMove": {"x": 1089, "y": 436}}, {"MouseMove": {"x": 1081, "y": 447}}, {"MouseMove": {"x": 1072, "y": 458}}, {"MouseMove": {"x": 1063, "y": 465}}, {"MouseMove": {"x": 1053, "y": 472}}, {"MouseMove": {"x": 1042, "y": 478}}, {"MouseMove": {"x": 1036, "y": 480}}, {"MouseMove": {"x": 1026, "y": 483}}, {"MouseMove": {"x": 1018, "y": 485}}, {"MouseMove": {"x": 1011, "y": 486}}, {"MouseMove": {"x": 1008, "y": 487}}, {"MouseMove": {"x": 1004, "y": 487}}, {"MouseMove": {"x": 1001, "y": 487}}, {"MouseMove": {"x": 996, "y": 487}}, {"MouseMove": {"x": 995, "y": 487}}, {"MouseMove": {"x": 992, "y": 482}}, {"MouseMove": {"x": 991, "y": 476}}, {"MouseMove": {"x": 990, "y": 469}}, {"MouseMove": {"x": 990, "y": 460}}, {"MouseMove": {"x": 990, "y": 449}}, {"MouseMove": {"x": 990, "y": 436}}, {"MouseMove": {"x": 990, "y": 422}}, {"MouseMove": {"x": 994, "y": 409}}, {"MouseMove": {"x": 998, "y": 396}}, {"MouseMove": {"x": 1003, "y": 382}}, {"MouseMove": {"x": 1010, "y": 369}}, {"MouseMove": {"x": 1017, "y": 358}}, {"MouseMove": {"x": 1028, "y": 348}}, {"MouseMove": {"x": 1039, "y": 340}}, {"MouseMove": {"x": 1051, "y": 334}}, {"MouseMove": {"x": 1065, "y": 328}}, {"MouseMove": {"x": 1081, "y": 325}}, {"MouseMove": {"x": 1095, "y": 323}}, {"MouseMove": {"x": 1101, "y": 323}}, {"MouseMove": {"x": 1112, "y": 323}}, {"MouseMove": {"x": 1116, "y": 323}}, {"MouseMove": {"x": 1122, "y": 323}}, {"MouseMove": {"x": 1127, "y": 323}}, {"MouseMove": {"x": 1130, "y": 323}}, {"MouseMove": {"x": 1133, "y": 324}}, {"MouseMove": {"x": 1133, "y": 326}}, {"MouseMove": {"x": 1133, "y": 330}}, {"MouseMove": {"x": 1133, "y": 334}}, {"MouseMove": {"x": 1131, "y": 340}}, {"MouseMove": {"x": 1125, "y": 346}}, {"MouseMove": {"x": 1112, "y": 360}}, {"MouseMove": {"x": 1100, "y": 370}}, {"MouseMove": {"x": 1083, "y": 381}}, {"MouseMove": {"x": 1062, "y": 395}}, {"MouseMove": {"x": 1039, "y": 405}}, {"MouseMove": {"x": 1013, "y": 415}}, {"MouseMove": {"x": 984, "y": 423}}, {"MouseMove": {"x": 956, "y": 428}}, {"MouseMove": {"x": 956, "y": 429}}, {"MouseMove": {"x": 956, "y": 431}}, {"MouseMove": {"x": 956, "y": 433}}, {"MouseMove": {"x": 956, "y": 434}}, {"MouseMove": {"x": 956, "y": 436}}, {"MouseMove": {"x": 955, "y": 439}}, {"MouseMove": {"x": 954, "y": 442}}, {"MouseMove": {"x": 953, "y": 445}}, {"MouseMove": {"x": 952, "y": 446}}, {"MouseMove": {"x": 951, "y": 448}}, {"MouseMove": {"x": 950, "y": 450}}, {"MouseMove": {"x": 948, "y": 452}}, {"MouseMove": {"x": 947, "y": 453}}, {"MouseMove": {"x": 946, "y": 455}}, {"MouseMove": {"x": 944, "y": 456}}, {"MouseMove": {"x": 943, "y": 457}}, {"MouseMove": {"x": 941, "y": 460}}, {"MouseMove": {"x": 940, "y": 461}}, {"MouseMove": {"x": 937, "y": 463}}, {"MouseMove": {"x": 935, "y": 464}}, {"MouseMove": {"x": 932, "y": 466}}, {"MouseMove": {"x": 931, "y": 466}}, {"MouseMove": {"x": 929, "y": 467}}, {"MouseMove": {"x": 927, "y": 469}}, {"MouseMove": {"x": 925, "y": 469}}, {"MouseMove": {"x": 923, "y": 470}}, {"MouseMove": {"x": 920, "y": 471}}, {"MouseMove": {"x": 918, "y": 471}}, {"MouseMove": {"x": 916, "y": 472}}, {"MouseMove": {"x": 913, "y": 472}}, {"MouseMove": {"x": 910, "y": 472}}, {"MouseMove": {"x": 907, "y": 472}}, {"MouseMove": {"x": 904, "y": 472}}, {"MouseMove": {"x": 901, "y": 471}}, {"MouseMove": {"x": 898, "y": 470}}, {"MouseMove": {"x": 895, "y": 468}}, {"MouseMove": {"x": 894, "y": 468}}, {"MouseMove": {"x": 891, "y": 466}}, {"MouseMove": {"x": 889, "y": 465}}, {"MouseMove": {"x": 886, "y": 464}}, {"MouseMove": {"x": 885, "y": 463}}, {"MouseMove": {"x": 883, "y": 462}}, {"MouseMove": {"x": 881, "y": 461}}, {"MouseMove": {"x": 879, "y": 460}}, {"MouseMove": {"x": 878, "y": 460}}, {"MouseMove": {"x": 877, "y": 459}}, {"MouseMove": {"x": 875, "y": 458}}, {"MouseMove": {"x": 874, "y": 457}}, {"MouseMove": {"x": 872, "y": 456}}, {"MouseMove": {"x": 871, "y": 455}}, {"MouseMove": {"x": 870, "y": 454}}, {"MouseMove": {"x": 869, "y": 454}}, {"MouseMove": {"x": 868, "y": 453}}, {"MouseMove": {"x": 868, "y": 453}}, {"MouseMove": {"x": 867, "y": 452}}, {"MouseMove": {"x": 867, "y": 452}}, {"MouseMove": {"x": 867, "y": 451}}, {"MouseMove": {"x": 866, "y": 451}}, {"MouseMove": {"x": 866, "y": 451}}, {"MouseMove": {"x": 866, "y": 451}}, {"MouseMove": {"x": 866, "y": 450}}, {"MouseClick": {"button": "Left", "x": 0, "y": 0}}], "created_at": "2025-05-26T02:46:36.945346Z", "modified_at": "2025-05-26T02:46:36.946088Z", "tags": []}