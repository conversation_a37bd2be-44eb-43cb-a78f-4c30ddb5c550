[package]
edition = "2024"
name = "acto"
version = "0.1.0"

[dependencies]
makepad-widgets = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
uuid = { version = "1.0", features = ["v4"] }
screenshots = "0.3"
image = "0.24"

# Platform-specific dependencies for automation
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "wingdi", "wincon"] }
windows = { version = "0.52", features = [
    "Win32_UI_WindowsAndMessaging",
    "Win32_UI_Input_KeyboardAndMouse",
    "Win32_Foundation",
] }

[target.'cfg(all(unix, not(target_os = "macos")))'.dependencies]
x11 = { version = "2.21", features = ["xlib", "xtest"] }
libc = "0.2"

[target.'cfg(target_os = "macos")'.dependencies]
core-graphics = "0.23"
core-foundation = "0.9"
