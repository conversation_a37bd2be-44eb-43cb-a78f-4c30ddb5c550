-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Process:               acto [19950]
Path:                  /Users/<USER>/*/acto
Identifier:            acto
Version:               ???
Code Type:             ARM-64 (Native)
Parent Process:        zsh [88889]
Responsible:           stable [88861]
User ID:               501

Date/Time:             2025-05-26 15:05:29.6629 +0800
OS Version:            macOS 15.3.2 (24D81)
Report Version:        12
Anonymous UUID:        9DA484F3-0748-7E1E-3E5E-1041B689DCC1

Sleep/Wake UUID:       BF897273-6AD1-4083-A881-FA6D7585037F

Time Awake Since Boot: 160000 seconds
Time Since Wake:       18439 seconds

System Integrity Protection: enabled

Crashed Thread:        8

Exception Type:        EXC_BREAKPOINT (SIGTRAP)
Exception Codes:       0x0000000000000001, 0x000000018563b9c0

Termination Reason:    Namespace SIGNAL, Code 5 Trace/BPT trap: 5
Terminating Process:   exc handler [19950]

Thread 0:: main Dispatch queue: com.apple.main-thread
0   libsystem_kernel.dylib        	       0x1857aaf54 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x1857bd604 mach_msg2_internal + 80
2   libsystem_kernel.dylib        	       0x1857b3af8 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x1857ab29c mach_msg + 24
4   CoreFoundation                	       0x1858d4a4c __CFRunLoopServiceMachPort + 160
5   CoreFoundation                	       0x1858d32ac __CFRunLoopRun + 1212
6   CoreFoundation                	       0x1858d2734 CFRunLoopRunSpecific + 588
7   HIToolbox                     	       0x190e41530 RunCurrentEventLoopInMode + 292
8   HIToolbox                     	       0x190e47348 ReceiveNextEventCommon + 676
9   HIToolbox                     	       0x190e47508 _BlockUntilNextEventMatchingListInModeWithFilter + 76
10  AppKit                        	       0x18944a848 _DPSNextEvent + 660
11  AppKit                        	       0x189db0c24 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:] + 688
12  acto                          	       0x100ec6b10 _$LT$$LP$A$C$B$C$C$C$D$RP$$u20$as$u20$makepad_objc_sys..message..MessageArguments$GT$::invoke::he2528af8e80c47e3 + 120
13  acto                          	       0x100eeee78 makepad_objc_sys::message::platform::send_unverified::hc6ab337c1298de7c + 168
14  acto                          	       0x100ead934 makepad_platform::os::apple::macos::macos_app::MacosApp::event_loop::h5980f458a5b4a616 + 3516
15  acto                          	       0x100f9abec makepad_platform::os::apple::macos::macos::_$LT$impl$u20$makepad_platform..cx..Cx$GT$::event_loop::h191e45e572229434 + 1700
16  acto                          	       0x1007d4644 acto::app::app_main::h9cf774291a8e12e4 + 1200
17  acto                          	       0x10079511c acto::main::h96e7cf32cf2bc77d + 104 (main.rs:5)
18  acto                          	       0x10079505c core::ops::function::FnOnce::call_once::h5ad2b69afef6c7a6 + 20 (function.rs:250)
19  acto                          	       0x100795014 std::sys::backtrace::__rust_begin_short_backtrace::hddc656742028e32f + 24 (backtrace.rs:152)
20  acto                          	       0x100794fe4 std::rt::lang_start::_$u7b$$u7b$closure$u7d$$u7d$::hdbe31cb447b2dd12 + 28 (rt.rs:199)
21  acto                          	       0x1013568d4 std::rt::lang_start_internal::h95cf27b851151b9c + 888
22  acto                          	       0x100794fbc std::rt::lang_start::hb8a9fe794931ca1f + 84 (rt.rs:198)
23  acto                          	       0x100795158 main + 36
24  dyld                          	       0x18546c274 start + 2840

Thread 1:
0   libsystem_kernel.dylib        	       0x1857aaed0 semaphore_wait_trap + 8
1   libdispatch.dylib             	       0x185639b50 _dispatch_sema4_wait + 28
2   libdispatch.dylib             	       0x18563a204 _dispatch_semaphore_wait_slow + 132
3   acto                          	       0x101357808 std::thread::Thread::park::haabbdd8eb2d3cab2 + 48
4   acto                          	       0x100fbf6dc std::sync::mpmc::context::Context::wait_until::hef6a4f0ee45fef9b + 676
5   acto                          	       0x100e96818 std::sync::mpmc::list::Channel$LT$T$GT$::recv::_$u7b$$u7b$closure$u7d$$u7d$::h02920db578edaf5b + 268
6   acto                          	       0x100fc0fd0 std::sync::mpmc::context::Context::with::_$u7b$$u7b$closure$u7d$$u7d$::h0b518d778aa15007 + 652
7   acto                          	       0x100edb808 std::thread::local::LocalKey$LT$T$GT$::try_with::hd5a9549c2caa931f + 172
8   acto                          	       0x100fc04d8 std::sync::mpmc::context::Context::with::hc52dc6d51890b7df + 52
9   acto                          	       0x100e96428 std::sync::mpmc::list::Channel$LT$T$GT$::recv::hbbd6323f3c3b3dfc + 344
10  acto                          	       0x100fb0688 std::sync::mpmc::Receiver$LT$T$GT$::recv::h97d6cb4d46ae0939 + 160
11  acto                          	       0x100fb0158 std::sync::mpmc::Receiver$LT$T$GT$::recv_timeout::h578939801255e6bc + 128
12  acto                          	       0x100f1f584 std::sync::mpsc::Receiver$LT$T$GT$::recv_timeout::h057dc2534dcc0bd5 + 32
13  acto                          	       0x100face30 makepad_platform::web_socket::_$LT$impl$u20$makepad_platform..cx..Cx$GT$::run_websocket_thread::_$u7b$$u7b$closure$u7d$$u7d$::hd067960ed728af4c + 352
14  acto                          	       0x100f77658 std::sys::backtrace::__rust_begin_short_backtrace::h3cceeb6b6557baaa + 28
15  acto                          	       0x101009bc8 std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::_$u7b$$u7b$closure$u7d$$u7d$::h2a4d333e4a502fda + 104
16  acto                          	       0x100f0d968 _$LT$core..panic..unwind_safe..AssertUnwindSafe$LT$F$GT$$u20$as$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$GT$::call_once::h43ab64438c56311f + 44
17  acto                          	       0x100eff5d4 std::panicking::try::do_call::h6fa7974ccb9dac31 + 68
18  acto                          	       0x10100f7dc __rust_try + 32
19  acto                          	       0x101009330 std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::hb7d1b18a64c3b60b + 724
20  acto                          	       0x100f21340 core::ops::function::FnOnce::call_once$u7b$$u7b$vtable.shim$u7d$$u7d$::h6fc5eb0be227ee11 + 24
21  acto                          	       0x101364be4 std::sys::pal::unix::thread::Thread::new::thread_start::h6d53b1b0c047a3b9 + 52
22  libsystem_pthread.dylib       	       0x1857ec2e4 _pthread_start + 136
23  libsystem_pthread.dylib       	       0x1857e70fc thread_start + 8

Thread 2:
0   libsystem_kernel.dylib        	       0x1857ae4e8 __semwait_signal + 8
1   libsystem_c.dylib             	       0x18568d6f0 nanosleep + 220
2   acto                          	       0x1013574d0 std::thread::sleep::h42c4fc7d81fd363d + 84
3   acto                          	       0x100ed0b8c makepad_platform::live_cx::_$LT$impl$u20$makepad_platform..cx..Cx$GT$::start_disk_live_file_watcher::_$u7b$$u7b$closure$u7d$$u7d$::ha31c71c699161b5a + 576
4   acto                          	       0x100f77614 std::sys::backtrace::__rust_begin_short_backtrace::h1d8a02ede95b5cad + 16
5   acto                          	       0x101009e8c std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::_$u7b$$u7b$closure$u7d$$u7d$::ha5320832351a1fb8 + 120
6   acto                          	       0x100f0d9a0 _$LT$core..panic..unwind_safe..AssertUnwindSafe$LT$F$GT$$u20$as$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$GT$::call_once::h59e1e4922bba4fc7 + 44
7   acto                          	       0x100eff6b0 std::panicking::try::do_call::h8a17398c5ed57a40 + 68
8   acto                          	       0x10100f7dc __rust_try + 32
9   acto                          	       0x10100977c std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::hd2c48c167864e8cb + 768
10  acto                          	       0x100f20e84 core::ops::function::FnOnce::call_once$u7b$$u7b$vtable.shim$u7d$$u7d$::h17c88178e9d901c8 + 24
11  acto                          	       0x101364be4 std::sys::pal::unix::thread::Thread::new::thread_start::h6d53b1b0c047a3b9 + 52
12  libsystem_pthread.dylib       	       0x1857ec2e4 _pthread_start + 136
13  libsystem_pthread.dylib       	       0x1857e70fc thread_start + 8

Thread 3:
0   libsystem_pthread.dylib       	       0x1857e70e8 start_wqthread + 0

Thread 4:
0   libsystem_pthread.dylib       	       0x1857e70e8 start_wqthread + 0

Thread 5:
0   libsystem_pthread.dylib       	       0x1857e70e8 start_wqthread + 0

Thread 6:: com.apple.NSEventThread
0   libsystem_kernel.dylib        	       0x1857aaf54 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x1857bd604 mach_msg2_internal + 80
2   libsystem_kernel.dylib        	       0x1857b3af8 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x1857ab29c mach_msg + 24
4   CoreFoundation                	       0x1858d4a4c __CFRunLoopServiceMachPort + 160
5   CoreFoundation                	       0x1858d32ac __CFRunLoopRun + 1212
6   CoreFoundation                	       0x1858d2734 CFRunLoopRunSpecific + 588
7   AppKit                        	       0x18956f278 _NSEventThread + 148
8   libsystem_pthread.dylib       	       0x1857ec2e4 _pthread_start + 136
9   libsystem_pthread.dylib       	       0x1857e70fc thread_start + 8

Thread 7:
0   libsystem_pthread.dylib       	       0x1857e70e8 start_wqthread + 0

Thread 8 Crashed:
0   libdispatch.dylib             	       0x18563b9c0 _dispatch_assert_queue_fail + 120
1   libdispatch.dylib             	       0x18563b948 dispatch_assert_queue + 196
2   HIToolbox                     	       0x190e67234 islGetInputSourceListWithAdditions + 160
3   HIToolbox                     	       0x190e5efc0 isValidateInputSourceRef + 92
4   HIToolbox                     	       0x190e5f058 TSMGetInputSourceProperty + 44
5   acto                          	       0x1009c2c70 rdev::macos::keyboard::Keyboard::string_from_code::h205be184e5d19a95 + 104
6   acto                          	       0x1009c2bfc rdev::macos::keyboard::Keyboard::create_string_for_key::h9df4c5e67cbd851d + 84
7   acto                          	       0x1009c2ad0 rdev::macos::common::convert::h2498d68943818bb7 + 1356
8   acto                          	       0x1009c34bc rdev::macos::listen::raw_callback::h2236c3ac0e4df9e0 + 264
9   SkyLight                      	       0x18b5b7f88 processEventTapData(void*, unsigned int, unsigned int, unsigned int, unsigned char*, unsigned int) + 560
10  SkyLight                      	       0x18b852604 _XPostEventTapData + 336
11  SkyLight                      	       0x18b5b8818 eventTapMessageHandler(__CFMachPort*, void*, long, void*) + 168
12  CoreFoundation                	       0x185901d10 __CFMachPortPerform + 248
13  CoreFoundation                	       0x1858d4e38 __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE1_PERFORM_FUNCTION__ + 60
14  CoreFoundation                	       0x1858d4d58 __CFRunLoopDoSource1 + 524
15  CoreFoundation                	       0x1858d36b8 __CFRunLoopRun + 2248
16  CoreFoundation                	       0x1858d2734 CFRunLoopRunSpecific + 588
17  CoreFoundation                	       0x18594d9d0 CFRunLoopRun + 64
18  acto                          	       0x1009c3748 rdev::macos::listen::listen::ha4123aa6dd4c3307 + 340
19  acto                          	       0x1009c378c rdev::listen::hcaed8d3f21f751e3 + 24
20  acto                          	       0x1007eae6c acto::automation::AutomationEngine::recording_loop::h21f54cca262169db + 68
21  acto                          	       0x100796114 acto::automation::AutomationEngine::start_recording::_$u7b$$u7b$closure$u7d$$u7d$::h6a4cfa2f37be6703 + 16
22  acto                          	       0x1007cac6c std::sys::backtrace::__rust_begin_short_backtrace::h7d065a54e79ba601 + 16
23  acto                          	       0x1007d6558 std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::_$u7b$$u7b$closure$u7d$$u7d$::haa598da4a01adef9 + 88
24  acto                          	       0x1007bfac0 _$LT$core..panic..unwind_safe..AssertUnwindSafe$LT$F$GT$$u20$as$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$GT$::call_once::hcb071a2d5b869057 + 40
25  acto                          	       0x1007e2860 std::panicking::try::do_call::h5cc741a6f17311d2 + 64
26  acto                          	       0x1007d65fc __rust_try + 32
27  acto                          	       0x1007d5ff4 std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::hfe32ea327395346c + 656
28  acto                          	       0x1007cae5c core::ops::function::FnOnce::call_once$u7b$$u7b$vtable.shim$u7d$$u7d$::ha5f67616e2362dfb + 24
29  acto                          	       0x101364be4 std::sys::pal::unix::thread::Thread::new::thread_start::h6d53b1b0c047a3b9 + 52
30  libsystem_pthread.dylib       	       0x1857ec2e4 _pthread_start + 136
31  libsystem_pthread.dylib       	       0x1857e70fc thread_start + 8


Thread 8 crashed with ARM Thread State (64-bit):
    x0: 0x00006000020c2b90   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x00006000020c2bc0
    x4: 0x00006000020c2c00   x5: 0x0000000000000000   x6: 0x0068637461707369   x7: 0x00006000020c2b80
    x8: 0x00000001ecd0f000   x9: 0x00000000b4c18864  x10: 0x000000000000007f  x11: 0x0000000000002b80
   x12: 0x00000000000007fb  x13: 0x00000000000007fd  x14: 0x00000000b4e19058  x15: 0x00000000b4c18864
   x16: 0x00000000b4e00000  x17: 0x0000000000000058  x18: 0x0000000000000000  x19: 0x00006000020c2880
   x20: 0x0000000000000000  x21: 0x000000000000005d  x22: 0x00000001ecfb7000  x23: 0x0000600000ecad80
   x24: 0x00006000032c1600  x25: 0x0000000000000228  x26: 0x00006000032c0f00  x27: 0x00000001ecfb7000
   x28: 0x00006000000bbe70   fp: 0x000000016ff40130   lr: 0x000000018563b9b8
    sp: 0x000000016ff400f0   pc: 0x000000018563b9c0 cpsr: 0x60001000
   far: 0x0000000000000000  esr: 0xf2000001 (Breakpoint) brk 1

Binary Images:
       0x100790000 -        0x101897fff acto (*) <512d3568-0c4b-361d-bbbc-71c4ec866c2e> /Users/<USER>/*/acto
       0x103d10000 -        0x1043b3fff com.apple.AGXMetalG13X (324.6) <d03677d4-c896-3fd7-88c0-abed05133e06> /System/Library/Extensions/AGXMetalG13X.bundle/Contents/MacOS/AGXMetalG13X
       0x107220000 -        0x10722bfff libobjc-trampolines.dylib (*) <3d687e9b-e092-3632-bc1d-74b19d492de0> /usr/lib/libobjc-trampolines.dylib
       0x1857aa000 -        0x1857e4ff7 libsystem_kernel.dylib (*) <eee9d0d3-dffc-37cb-9ced-b27cd0286d8c> /usr/lib/system/libsystem_kernel.dylib
       0x185857000 -        0x185d4bfff com.apple.CoreFoundation (6.9) <190e6a36-fcaa-3ea3-94bb-7009c44653da> /System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
       0x190d36000 -        0x19103dfff com.apple.HIToolbox (2.1.1) <950f1236-acaf-379d-819f-6c6b0b5deabd> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
       0x18940f000 -        0x18a84bfff com.apple.AppKit (6.9) <b88a44c1-d617-33dc-90ed-b6ab417c428e> /System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
       0x185466000 -        0x1854e7f3f dyld (*) <398a133c-9bcb-317f-a064-a40d3cea3c0f> /usr/lib/dyld
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
       0x185635000 -        0x18567bfff libdispatch.dylib (*) <5576e4fd-aad2-3608-8c8f-4eec421236f9> /usr/lib/system/libdispatch.dylib
       0x1857e5000 -        0x1857f1fff libsystem_pthread.dylib (*) <642faf7a-874e-37e6-8aba-2b0cc09a3025> /usr/lib/system/libsystem_pthread.dylib
       0x18567f000 -        0x185700ffb libsystem_c.dylib (*) <92699527-645f-3d8d-aed8-1cfb0c034e15> /usr/lib/system/libsystem_c.dylib
       0x18b41f000 -        0x18b94bfff com.apple.SkyLight (1.600.0) <f66e0c94-99b5-3fcd-b726-3e3cbca668a3> /System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight

External Modification Summary:
  Calls made by other processes targeting this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by all processes on this machine:
    task_for_pid: 1
    thread_create: 0
    thread_set_state: 20

VM Region Summary:
ReadOnly portion of Libraries: Total=1.6G resident=0K(0%) swapped_out_or_unallocated=1.6G(100%)
Writable regions: Total=826.1M written=562K(0%) resident=562K(0%) swapped_out=0K(0%) unallocated=825.6M(100%)

                                VIRTUAL   REGION 
REGION TYPE                        SIZE    COUNT (non-coalesced) 
===========                     =======  ======= 
Accelerate framework               128K        1 
Activity Tracing                   256K        1 
CG image                            96K        4 
ColorSync                          560K       27 
CoreAnimation                      432K       27 
CoreGraphics                        48K        3 
CoreServices                       624K        2 
CoreUI image data                  880K        6 
Foundation                          16K        1 
Kernel Alloc Once                   32K        1 
MALLOC                           806.8M       48 
MALLOC guard page                  192K       12 
STACK GUARD                        128K        8 
Stack                             16.7M        9 
Stack Guard                       56.0M        1 
VM_ALLOCATE                        304K       19 
VM_ALLOCATE (reserved)             512K        4         reserved VM address space (unallocated)
__AUTH                            5134K      655 
__AUTH_CONST                      69.3M      895 
__CTF                               824        1 
__DATA                            24.1M      876 
__DATA_CONST                      24.3M      905 
__DATA_DIRTY                      2748K      333 
__FONT_DATA                        2352        1 
__INFO_FILTER                         8        1 
__LINKEDIT                       621.3M        4 
__OBJC_RW                         2374K        1 
__TEXT                             1.0G      925 
__TPRO_CONST                       272K        2 
mapped file                      231.1M       25 
owned unmapped memory              320K        1 
page table in kernel               562K        1 
shared memory                      848K       12 
===========                     =======  ======= 
TOTAL                              2.8G     4812 
TOTAL, minus reserved VM space     2.8G     4812 



-----------
Full Report
-----------

{"app_name":"acto","timestamp":"2025-05-26 15:05:30.00 +0800","app_version":"","slice_uuid":"512d3568-0c4b-361d-bbbc-71c4ec866c2e","build_version":"","platform":1,"share_with_app_devs":0,"is_first_party":1,"bug_type":"309","os_version":"macOS 15.3.2 (24D81)","roots_installed":0,"incident_id":"F1D1BBFE-9F1B-49ED-B19C-2C9124190ED1","name":"acto"}
{
  "uptime" : 160000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "MacBookPro18,3",
  "coalitionID" : 38904,
  "osVersion" : {
    "train" : "macOS 15.3.2",
    "build" : "24D81",
    "releaseType" : "User"
  },
  "captureTime" : "2025-05-26 15:05:29.6629 +0800",
  "codeSigningMonitor" : 1,
  "incident" : "F1D1BBFE-9F1B-49ED-B19C-2C9124190ED1",
  "pid" : 19950,
  "translated" : false,
  "cpuType" : "ARM-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-05-26 15:05:25.6293 +0800",
  "procStartAbsTime" : 3969672467655,
  "procExitAbsTime" : 3969768997410,
  "procName" : "acto",
  "procPath" : "\/Users\/<USER>\/*\/acto",
  "parentProc" : "zsh",
  "parentPid" : 88889,
  "coalitionName" : "dev.warp.Warp-Stable",
  "crashReporterKey" : "9DA484F3-0748-7E1E-3E5E-1041B689DCC1",
  "responsiblePid" : 88861,
  "responsibleProc" : "stable",
  "codeSigningID" : "acto-64944e72f9b4a73b",
  "codeSigningTeamID" : "",
  "codeSigningFlags" : 570556929,
  "codeSigningValidationCategory" : 10,
  "codeSigningTrustLevel" : 4294967295,
  "instructionByteStream" : {"beforePC":"IaAUkaBjANEc3QCUs4Ne+PMDAPnAAQDwAEwWkfTMAJSotjOQE0EE+Q==","atPC":"IAAg1H8jA9X9e7+p\/QMAkRAAQPnxAwCqMVzt8jAawdrxAxCq8UfB2g=="},
  "bootSessionUUID" : "2AE3BEF4-C063-4A35-A8F1-F2EAACB4FC81",
  "wakeTime" : 18439,
  "sleepWakeUUID" : "BF897273-6AD1-4083-A881-FA6D7585037F",
  "sip" : "enabled",
  "exception" : {"codes":"0x0000000000000001, 0x000000018563b9c0","rawCodes":[1,6532872640],"type":"EXC_BREAKPOINT","signal":"SIGTRAP"},
  "termination" : {"flags":0,"code":5,"namespace":"SIGNAL","indicator":"Trace\/BPT trap: 5","byProc":"exc handler","byPid":19950},
  "os_fault" : {"process":"acto"},
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":20,"task_for_pid":1},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "faultingThread" : 8,
  "threads" : [{"threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":60537564037120},{"value":0},{"value":60537564037120},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":14095},{"value":1792},{"value":0},{"value":18446744073709551569},{"value":8446909944},{"value":0},{"value":4294967295},{"value":2},{"value":60537564037120},{"value":0},{"value":60537564037120},{"value":6163965976},{"value":8589934592},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6534452740},"cpsr":{"value":4096},"fp":{"value":6163965824},"sp":{"value":6163965744},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6534377300},"far":{"value":0}},"id":9568176,"name":"main","queue":"com.apple.main-thread","frames":[{"imageOffset":3924,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":3},{"imageOffset":79364,"symbol":"mach_msg2_internal","symbolLocation":80,"imageIndex":3},{"imageOffset":39672,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":3},{"imageOffset":4764,"symbol":"mach_msg","symbolLocation":24,"imageIndex":3},{"imageOffset":514636,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":4},{"imageOffset":508588,"symbol":"__CFRunLoopRun","symbolLocation":1212,"imageIndex":4},{"imageOffset":505652,"symbol":"CFRunLoopRunSpecific","symbolLocation":588,"imageIndex":4},{"imageOffset":1094960,"symbol":"RunCurrentEventLoopInMode","symbolLocation":292,"imageIndex":5},{"imageOffset":1119048,"symbol":"ReceiveNextEventCommon","symbolLocation":676,"imageIndex":5},{"imageOffset":1119496,"symbol":"_BlockUntilNextEventMatchingListInModeWithFilter","symbolLocation":76,"imageIndex":5},{"imageOffset":243784,"symbol":"_DPSNextEvent","symbolLocation":660,"imageIndex":6},{"imageOffset":********,"symbol":"-[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]","symbolLocation":688,"imageIndex":6},{"imageOffset":7564048,"symbol":"_$LT$$LP$A$C$B$C$C$C$D$RP$$u20$as$u20$makepad_objc_sys..message..MessageArguments$GT$::invoke::he2528af8e80c47e3","symbolLocation":120,"imageIndex":0},{"imageOffset":7728760,"symbol":"makepad_objc_sys::message::platform::send_unverified::hc6ab337c1298de7c","symbolLocation":168,"imageIndex":0},{"imageOffset":7461172,"symbol":"makepad_platform::os::apple::macos::macos_app::MacosApp::event_loop::h5980f458a5b4a616","symbolLocation":3516,"imageIndex":0},{"imageOffset":8432620,"symbol":"makepad_platform::os::apple::macos::macos::_$LT$impl$u20$makepad_platform..cx..Cx$GT$::event_loop::h191e45e572229434","symbolLocation":1700,"imageIndex":0},{"imageOffset":280132,"symbol":"acto::app::app_main::h9cf774291a8e12e4","symbolLocation":1200,"imageIndex":0},{"imageOffset":20764,"sourceLine":5,"sourceFile":"main.rs","symbol":"acto::main::h96e7cf32cf2bc77d","imageIndex":0,"symbolLocation":104},{"imageOffset":20572,"sourceLine":250,"sourceFile":"function.rs","symbol":"core::ops::function::FnOnce::call_once::h5ad2b69afef6c7a6","imageIndex":0,"symbolLocation":20},{"imageOffset":20500,"sourceLine":152,"sourceFile":"backtrace.rs","symbol":"std::sys::backtrace::__rust_begin_short_backtrace::hddc656742028e32f","imageIndex":0,"symbolLocation":24},{"imageOffset":20452,"sourceLine":199,"sourceFile":"rt.rs","symbol":"std::rt::lang_start::_$u7b$$u7b$closure$u7d$$u7d$::hdbe31cb447b2dd12","imageIndex":0,"symbolLocation":28},{"imageOffset":12347604,"symbol":"std::rt::lang_start_internal::h95cf27b851151b9c","symbolLocation":888,"imageIndex":0},{"imageOffset":20412,"sourceLine":198,"sourceFile":"rt.rs","symbol":"std::rt::lang_start::hb8a9fe794931ca1f","imageIndex":0,"symbolLocation":84},{"imageOffset":20824,"symbol":"main","symbolLocation":36,"imageIndex":0},{"imageOffset":25204,"symbol":"start","symbolLocation":2840,"imageIndex":7}]},{"id":9568177,"frames":[{"imageOffset":3792,"symbol":"semaphore_wait_trap","symbolLocation":8,"imageIndex":3},{"imageOffset":19280,"symbol":"_dispatch_sema4_wait","symbolLocation":28,"imageIndex":9},{"imageOffset":20996,"symbol":"_dispatch_semaphore_wait_slow","symbolLocation":132,"imageIndex":9},{"imageOffset":12351496,"symbol":"std::thread::Thread::park::haabbdd8eb2d3cab2","symbolLocation":48,"imageIndex":0},{"imageOffset":8582876,"symbol":"std::sync::mpmc::context::Context::wait_until::hef6a4f0ee45fef9b","symbolLocation":676,"imageIndex":0},{"imageOffset":7366680,"symbol":"std::sync::mpmc::list::Channel$LT$T$GT$::recv::_$u7b$$u7b$closure$u7d$$u7d$::h02920db578edaf5b","symbolLocation":268,"imageIndex":0},{"imageOffset":8589264,"symbol":"std::sync::mpmc::context::Context::with::_$u7b$$u7b$closure$u7d$$u7d$::h0b518d778aa15007","symbolLocation":652,"imageIndex":0},{"imageOffset":7649288,"symbol":"std::thread::local::LocalKey$LT$T$GT$::try_with::hd5a9549c2caa931f","symbolLocation":172,"imageIndex":0},{"imageOffset":8586456,"symbol":"std::sync::mpmc::context::Context::with::hc52dc6d51890b7df","symbolLocation":52,"imageIndex":0},{"imageOffset":7365672,"symbol":"std::sync::mpmc::list::Channel$LT$T$GT$::recv::hbbd6323f3c3b3dfc","symbolLocation":344,"imageIndex":0},{"imageOffset":8521352,"symbol":"std::sync::mpmc::Receiver$LT$T$GT$::recv::h97d6cb4d46ae0939","symbolLocation":160,"imageIndex":0},{"imageOffset":8520024,"symbol":"std::sync::mpmc::Receiver$LT$T$GT$::recv_timeout::h578939801255e6bc","symbolLocation":128,"imageIndex":0},{"imageOffset":7927172,"symbol":"std::sync::mpsc::Receiver$LT$T$GT$::recv_timeout::h057dc2534dcc0bd5","symbolLocation":32,"imageIndex":0},{"imageOffset":8506928,"symbol":"makepad_platform::web_socket::_$LT$impl$u20$makepad_platform..cx..Cx$GT$::run_websocket_thread::_$u7b$$u7b$closure$u7d$$u7d$::hd067960ed728af4c","symbolLocation":352,"imageIndex":0},{"imageOffset":8287832,"symbol":"std::sys::backtrace::__rust_begin_short_backtrace::h3cceeb6b6557baaa","symbolLocation":28,"imageIndex":0},{"imageOffset":8887240,"symbol":"std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::_$u7b$$u7b$closure$u7d$$u7d$::h2a4d333e4a502fda","symbolLocation":104,"imageIndex":0},{"imageOffset":7854440,"symbol":"_$LT$core..panic..unwind_safe..AssertUnwindSafe$LT$F$GT$$u20$as$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$GT$::call_once::h43ab64438c56311f","symbolLocation":44,"imageIndex":0},{"imageOffset":7796180,"symbol":"std::panicking::try::do_call::h6fa7974ccb9dac31","symbolLocation":68,"imageIndex":0},{"imageOffset":8910812,"symbol":"__rust_try","symbolLocation":32,"imageIndex":0},{"imageOffset":8885040,"symbol":"std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::hb7d1b18a64c3b60b","symbolLocation":724,"imageIndex":0},{"imageOffset":7934784,"symbol":"core::ops::function::FnOnce::call_once$u7b$$u7b$vtable.shim$u7d$$u7d$::h6fc5eb0be227ee11","symbolLocation":24,"imageIndex":0},{"imageOffset":12405732,"symbol":"std::sys::pal::unix::thread::Thread::new::thread_start::h6d53b1b0c047a3b9","symbolLocation":52,"imageIndex":0},{"imageOffset":29412,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":10},{"imageOffset":8444,"symbol":"thread_start","symbolLocation":8,"imageIndex":10}],"threadState":{"x":[{"value":14},{"value":8589934595},{"value":171798697235},{"value":11008001180163},{"value":14680198217728},{"value":11008001179648},{"value":48},{"value":0},{"value":0},{"value":1},{"value":0},{"value":2},{"value":105553161207808},{"value":2043},{"value":2147831803},{"value":0},{"value":18446744073709551580},{"value":8446929568},{"value":0},{"value":105553164342576},{"value":105553164342512},{"value":18446744073709551615},{"value":4321328440,"symbolLocation":624,"symbol":"makepad_platform::os::apple::metal_xpc::store_xpc_service_texture::DESCRIPTOR::h4f5be111720a7084"},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532864848},"cpsr":{"value":1610616832},"fp":{"value":6166108480},"sp":{"value":6166108464},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6534377168},"far":{"value":0}}},{"id":9568180,"frames":[{"imageOffset":17640,"symbol":"__semwait_signal","symbolLocation":8,"imageIndex":3},{"imageOffset":59120,"symbol":"nanosleep","symbolLocation":220,"imageIndex":11},{"imageOffset":12350672,"symbol":"std::thread::sleep::h42c4fc7d81fd363d","symbolLocation":84,"imageIndex":0},{"imageOffset":7605132,"symbol":"makepad_platform::live_cx::_$LT$impl$u20$makepad_platform..cx..Cx$GT$::start_disk_live_file_watcher::_$u7b$$u7b$closure$u7d$$u7d$::ha31c71c699161b5a","symbolLocation":576,"imageIndex":0},{"imageOffset":8287764,"symbol":"std::sys::backtrace::__rust_begin_short_backtrace::h1d8a02ede95b5cad","symbolLocation":16,"imageIndex":0},{"imageOffset":8887948,"symbol":"std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::_$u7b$$u7b$closure$u7d$$u7d$::ha5320832351a1fb8","symbolLocation":120,"imageIndex":0},{"imageOffset":7854496,"symbol":"_$LT$core..panic..unwind_safe..AssertUnwindSafe$LT$F$GT$$u20$as$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$GT$::call_once::h59e1e4922bba4fc7","symbolLocation":44,"imageIndex":0},{"imageOffset":7796400,"symbol":"std::panicking::try::do_call::h8a17398c5ed57a40","symbolLocation":68,"imageIndex":0},{"imageOffset":8910812,"symbol":"__rust_try","symbolLocation":32,"imageIndex":0},{"imageOffset":8886140,"symbol":"std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::hd2c48c167864e8cb","symbolLocation":768,"imageIndex":0},{"imageOffset":7933572,"symbol":"core::ops::function::FnOnce::call_once$u7b$$u7b$vtable.shim$u7d$$u7d$::h17c88178e9d901c8","symbolLocation":24,"imageIndex":0},{"imageOffset":12405732,"symbol":"std::sys::pal::unix::thread::Thread::new::thread_start::h6d53b1b0c047a3b9","symbolLocation":52,"imageIndex":0},{"imageOffset":29412,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":10},{"imageOffset":8444,"symbol":"thread_start","symbolLocation":8,"imageIndex":10}],"threadState":{"x":[{"value":4},{"value":0},{"value":1},{"value":1},{"value":0},{"value":100000000},{"value":52},{"value":0},{"value":8306187040,"symbolLocation":0,"symbol":"clock_sem"},{"value":16387},{"value":17},{"value":8335872},{"value":5125505024},{"value":5125488640},{"value":38},{"value":4294934015},{"value":334},{"value":8446927600},{"value":0},{"value":6168257944},{"value":6168257944},{"value":105553161131552},{"value":4321328376,"symbolLocation":560,"symbol":"makepad_platform::os::apple::metal_xpc::store_xpc_service_texture::DESCRIPTOR::h4f5be111720a7084"},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6533207792},"cpsr":{"value":1610616832},"fp":{"value":6168257920},"sp":{"value":6168257872},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6534391016},"far":{"value":0}}},{"id":9568181,"frames":[{"imageOffset":8424,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":10}],"threadState":{"x":[{"value":6168834048},{"value":8707},{"value":6168297472},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6168834048},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6534623464},"far":{"value":0}}},{"id":9568182,"frames":[{"imageOffset":8424,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":10}],"threadState":{"x":[{"value":6169407488},{"value":9475},{"value":6168870912},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6169407488},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6534623464},"far":{"value":0}}},{"id":9568192,"frames":[{"imageOffset":8424,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":10}],"threadState":{"x":[{"value":6169980928},{"value":43779},{"value":6169444352},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6169980928},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6534623464},"far":{"value":0}}},{"id":9568197,"name":"com.apple.NSEventThread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":177034256973824},{"value":0},{"value":177034256973824},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":41219},{"value":0},{"value":0},{"value":18446744073709551569},{"value":8446909944},{"value":0},{"value":4294967295},{"value":2},{"value":177034256973824},{"value":0},{"value":177034256973824},{"value":6170550376},{"value":8589934592},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6534452740},"cpsr":{"value":4096},"fp":{"value":6170550224},"sp":{"value":6170550144},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6534377300},"far":{"value":0}},"frames":[{"imageOffset":3924,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":3},{"imageOffset":79364,"symbol":"mach_msg2_internal","symbolLocation":80,"imageIndex":3},{"imageOffset":39672,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":3},{"imageOffset":4764,"symbol":"mach_msg","symbolLocation":24,"imageIndex":3},{"imageOffset":514636,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":4},{"imageOffset":508588,"symbol":"__CFRunLoopRun","symbolLocation":1212,"imageIndex":4},{"imageOffset":505652,"symbol":"CFRunLoopRunSpecific","symbolLocation":588,"imageIndex":4},{"imageOffset":1442424,"symbol":"_NSEventThread","symbolLocation":148,"imageIndex":6},{"imageOffset":29412,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":10},{"imageOffset":8444,"symbol":"thread_start","symbolLocation":8,"imageIndex":10}]},{"id":9568316,"frames":[{"imageOffset":8424,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":10}],"threadState":{"x":[{"value":6171127808},{"value":0},{"value":6170591232},{"value":0},{"value":278532},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6171127808},"esr":{"value":0,"description":" Address size fault"},"pc":{"value":6534623464},"far":{"value":0}}},{"triggered":true,"id":9568350,"threadState":{"x":[{"value":105553150618512},{"value":0},{"value":0},{"value":105553150618560},{"value":105553150618624},{"value":0},{"value":29382749080023913},{"value":105553150618496},{"value":8268083200,"symbolLocation":72,"symbol":"_OBJC_PROTOCOL_$_OS_dispatch_source_data_or"},{"value":3032582244},{"value":127},{"value":11136},{"value":2043},{"value":2045},{"value":3034681432},{"value":3032582244},{"value":3034578944},{"value":88},{"value":0},{"value":105553150617728},{"value":0},{"value":93},{"value":8270868480,"symbolLocation":4,"symbol":"SyncWithKeyPrefs()::sCGKeys"},{"value":105553131777408},{"value":105553169487360},{"value":552},{"value":105553169485568},{"value":8270868480,"symbolLocation":4,"symbol":"SyncWithKeyPrefs()::sCGKeys"},{"value":105553117036144}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6532872632},"cpsr":{"value":1610616832},"fp":{"value":6173229360},"sp":{"value":6173229296},"esr":{"value":4060086273,"description":"(Breakpoint) brk 1"},"pc":{"value":6532872640,"matchesCrashFrame":1},"far":{"value":0}},"frames":[{"imageOffset":27072,"symbol":"_dispatch_assert_queue_fail","symbolLocation":120,"imageIndex":9},{"imageOffset":26952,"symbol":"dispatch_assert_queue","symbolLocation":196,"imageIndex":9},{"imageOffset":1249844,"symbol":"islGetInputSourceListWithAdditions","symbolLocation":160,"imageIndex":5},{"imageOffset":1216448,"symbol":"isValidateInputSourceRef","symbolLocation":92,"imageIndex":5},{"imageOffset":1216600,"symbol":"TSMGetInputSourceProperty","symbolLocation":44,"imageIndex":5},{"imageOffset":2305136,"symbol":"rdev::macos::keyboard::Keyboard::string_from_code::h205be184e5d19a95","symbolLocation":104,"imageIndex":0},{"imageOffset":2305020,"symbol":"rdev::macos::keyboard::Keyboard::create_string_for_key::h9df4c5e67cbd851d","symbolLocation":84,"imageIndex":0},{"imageOffset":2304720,"symbol":"rdev::macos::common::convert::h2498d68943818bb7","symbolLocation":1356,"imageIndex":0},{"imageOffset":2307260,"symbol":"rdev::macos::listen::raw_callback::h2236c3ac0e4df9e0","symbolLocation":264,"imageIndex":0},{"imageOffset":1675144,"symbol":"processEventTapData(void*, unsigned int, unsigned int, unsigned int, unsigned char*, unsigned int)","symbolLocation":560,"imageIndex":12},{"imageOffset":4404740,"symbol":"_XPostEventTapData","symbolLocation":336,"imageIndex":12},{"imageOffset":1677336,"symbol":"eventTapMessageHandler(__CFMachPort*, void*, long, void*)","symbolLocation":168,"imageIndex":12},{"imageOffset":699664,"symbol":"__CFMachPortPerform","symbolLocation":248,"imageIndex":4},{"imageOffset":515640,"symbol":"__CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE1_PERFORM_FUNCTION__","symbolLocation":60,"imageIndex":4},{"imageOffset":515416,"symbol":"__CFRunLoopDoSource1","symbolLocation":524,"imageIndex":4},{"imageOffset":509624,"symbol":"__CFRunLoopRun","symbolLocation":2248,"imageIndex":4},{"imageOffset":505652,"symbol":"CFRunLoopRunSpecific","symbolLocation":588,"imageIndex":4},{"imageOffset":1010128,"symbol":"CFRunLoopRun","symbolLocation":64,"imageIndex":4},{"imageOffset":2307912,"symbol":"rdev::macos::listen::listen::ha4123aa6dd4c3307","symbolLocation":340,"imageIndex":0},{"imageOffset":2307980,"symbol":"rdev::listen::hcaed8d3f21f751e3","symbolLocation":24,"imageIndex":0},{"imageOffset":372332,"symbol":"acto::automation::AutomationEngine::recording_loop::h21f54cca262169db","symbolLocation":68,"imageIndex":0},{"imageOffset":24852,"symbol":"acto::automation::AutomationEngine::start_recording::_$u7b$$u7b$closure$u7d$$u7d$::h6a4cfa2f37be6703","symbolLocation":16,"imageIndex":0},{"imageOffset":240748,"symbol":"std::sys::backtrace::__rust_begin_short_backtrace::h7d065a54e79ba601","symbolLocation":16,"imageIndex":0},{"imageOffset":288088,"symbol":"std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::_$u7b$$u7b$closure$u7d$$u7d$::haa598da4a01adef9","symbolLocation":88,"imageIndex":0},{"imageOffset":195264,"symbol":"_$LT$core..panic..unwind_safe..AssertUnwindSafe$LT$F$GT$$u20$as$u20$core..ops..function..FnOnce$LT$$LP$$RP$$GT$$GT$::call_once::hcb071a2d5b869057","symbolLocation":40,"imageIndex":0},{"imageOffset":338016,"symbol":"std::panicking::try::do_call::h5cc741a6f17311d2","symbolLocation":64,"imageIndex":0},{"imageOffset":288252,"symbol":"__rust_try","symbolLocation":32,"imageIndex":0},{"imageOffset":286708,"symbol":"std::thread::Builder::spawn_unchecked_::_$u7b$$u7b$closure$u7d$$u7d$::hfe32ea327395346c","symbolLocation":656,"imageIndex":0},{"imageOffset":241244,"symbol":"core::ops::function::FnOnce::call_once$u7b$$u7b$vtable.shim$u7d$$u7d$::ha5f67616e2362dfb","symbolLocation":24,"imageIndex":0},{"imageOffset":12405732,"symbol":"std::sys::pal::unix::thread::Thread::new::thread_start::h6d53b1b0c047a3b9","symbolLocation":52,"imageIndex":0},{"imageOffset":29412,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":10},{"imageOffset":8444,"symbol":"thread_start","symbolLocation":8,"imageIndex":10}]}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4302897152,
    "size" : 17858560,
    "uuid" : "512d3568-0c4b-361d-bbbc-71c4ec866c2e",
    "path" : "\/Users\/<USER>\/*\/acto",
    "name" : "acto"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4358995968,
    "CFBundleShortVersionString" : "324.6",
    "CFBundleIdentifier" : "com.apple.AGXMetalG13X",
    "size" : 6963200,
    "uuid" : "d03677d4-c896-3fd7-88c0-abed05133e06",
    "path" : "\/System\/Library\/Extensions\/AGXMetalG13X.bundle\/Contents\/MacOS\/AGXMetalG13X",
    "name" : "AGXMetalG13X",
    "CFBundleVersion" : "324.6"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4414636032,
    "size" : 49152,
    "uuid" : "3d687e9b-e092-3632-bc1d-74b19d492de0",
    "path" : "\/usr\/lib\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6534373376,
    "size" : 241656,
    "uuid" : "eee9d0d3-dffc-37cb-9ced-b27cd0286d8c",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6535081984,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 5197824,
    "uuid" : "190e6a36-fcaa-3ea3-94bb-7009c44653da",
    "path" : "\/System\/Library\/Frameworks\/CoreFoundation.framework\/Versions\/A\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "3302.1.400"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6724739072,
    "CFBundleShortVersionString" : "2.1.1",
    "CFBundleIdentifier" : "com.apple.HIToolbox",
    "size" : 3178496,
    "uuid" : "950f1236-acaf-379d-819f-6c6b0b5deabd",
    "path" : "\/System\/Library\/Frameworks\/Carbon.framework\/Versions\/A\/Frameworks\/HIToolbox.framework\/Versions\/A\/HIToolbox",
    "name" : "HIToolbox"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6597701632,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.AppKit",
    "size" : 21221376,
    "uuid" : "b88a44c1-d617-33dc-90ed-b6ab417c428e",
    "path" : "\/System\/Library\/Frameworks\/AppKit.framework\/Versions\/C\/AppKit",
    "name" : "AppKit",
    "CFBundleVersion" : "2575.40.6"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6530949120,
    "size" : 532288,
    "uuid" : "398a133c-9bcb-317f-a064-a40d3cea3c0f",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6532845568,
    "size" : 290816,
    "uuid" : "5576e4fd-aad2-3608-8c8f-4eec421236f9",
    "path" : "\/usr\/lib\/system\/libdispatch.dylib",
    "name" : "libdispatch.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6534615040,
    "size" : 53248,
    "uuid" : "642faf7a-874e-37e6-8aba-2b0cc09a3025",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6533148672,
    "size" : 532476,
    "uuid" : "92699527-645f-3d8d-aed8-1cfb0c034e15",
    "path" : "\/usr\/lib\/system\/libsystem_c.dylib",
    "name" : "libsystem_c.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6631321600,
    "CFBundleShortVersionString" : "1.600.0",
    "CFBundleIdentifier" : "com.apple.SkyLight",
    "size" : 5427200,
    "uuid" : "f66e0c94-99b5-3fcd-b726-3e3cbca668a3",
    "path" : "\/System\/Library\/PrivateFrameworks\/SkyLight.framework\/Versions\/A\/SkyLight",
    "name" : "SkyLight"
  }
],
  "sharedCache" : {
  "base" : 6530138112,
  "size" : 4865835008,
  "uuid" : "5700d77a-6190-36e0-884d-fee6d586d62f"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.6G resident=0K(0%) swapped_out_or_unallocated=1.6G(100%)\nWritable regions: Total=826.1M written=562K(0%) resident=562K(0%) swapped_out=0K(0%) unallocated=825.6M(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nAccelerate framework               128K        1 \nActivity Tracing                   256K        1 \nCG image                            96K        4 \nColorSync                          560K       27 \nCoreAnimation                      432K       27 \nCoreGraphics                        48K        3 \nCoreServices                       624K        2 \nCoreUI image data                  880K        6 \nFoundation                          16K        1 \nKernel Alloc Once                   32K        1 \nMALLOC                           806.8M       48 \nMALLOC guard page                  192K       12 \nSTACK GUARD                        128K        8 \nStack                             16.7M        9 \nStack Guard                       56.0M        1 \nVM_ALLOCATE                        304K       19 \nVM_ALLOCATE (reserved)             512K        4         reserved VM address space (unallocated)\n__AUTH                            5134K      655 \n__AUTH_CONST                      69.3M      895 \n__CTF                               824        1 \n__DATA                            24.1M      876 \n__DATA_CONST                      24.3M      905 \n__DATA_DIRTY                      2748K      333 \n__FONT_DATA                        2352        1 \n__INFO_FILTER                         8        1 \n__LINKEDIT                       621.3M        4 \n__OBJC_RW                         2374K        1 \n__TEXT                             1.0G      925 \n__TPRO_CONST                       272K        2 \nmapped file                      231.1M       25 \nowned unmapped memory              320K        1 \npage table in kernel               562K        1 \nshared memory                      848K       12 \n===========                     =======  ======= \nTOTAL                              2.8G     4812 \nTOTAL, minus reserved VM space     2.8G     4812 \n",
  "legacyInfo" : {
  "threadTriggered" : {

  }
},
  "logWritingSignature" : "ba9a9eef04e6a7f681163c8cab9a39861c692251",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "64c17a9925d75a7281053d4c",
      "factorPackIds" : {
        "SIRI_AUDIO_DISABLE_MEDIA_ENTITY_SYNC" : "64d29746ad29a465b3bbeace"
      },
      "deploymentId" : 240000001
    },
    {
      "rolloutId" : "645197bf528fbf3c3af54105",
      "factorPackIds" : {
        "SIRI_VALUE_INFERENCE_PERVASIVE_ENTITY_RESOLUTION" : "663e65b4a1526e1ca0e288a1"
      },
      "deploymentId" : 240000002
    }
  ],
  "experiments" : [

  ]
}
}

Model: MacBookPro18,3, BootROM 11881.81.4, proc 8:6:2 processors, 32 GB, SMC 
Graphics: Apple M1 Pro, Apple M1 Pro, Built-In
Display: Color LCD, 3024 x 1964 Retina, Main, MirrorOff, Online
Display: BenQ BL2480T, 1920 x 1080 (1080p FHD - Full High Definition), MirrorOff, Online
Memory Module: LPDDR5, Hynix
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0x4387), wl0: Oct 31 2024 06:06:06 version 20.10.1135.4.8.7.191 FWID 01-e648b845
IO80211_driverkit-1345.10 "IO80211_driverkit-1345.10" Dec 14 2024 17:47:07
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en0
USB Device: USB31Bus
USB Device: USB31Bus
USB Device: USB31Bus
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
