use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::fs;
use crate::utils::get_config_path;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub general: GeneralConfig,
    pub automation: AutomationConfig,
    pub ui: UIConfig,
    pub logging: LoggingConfig,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct GeneralConfig {
    pub auto_start: bool,
    pub minimize_to_tray: bool,
    pub check_updates: bool,
    pub language: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutomationConfig {
    pub default_delay: u64,  // 毫秒
    pub max_execution_time: u64,  // 秒
    pub enable_hotkeys: bool,
    pub record_mouse_moves: bool,
    pub record_window_events: bool,
    pub screenshot_on_error: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UIConfig {
    pub theme: String,
    pub window_width: u32,
    pub window_height: u32,
    pub window_x: i32,
    pub window_y: i32,
    pub always_on_top: bool,
    pub show_toolbar: bool,
    pub show_status_bar: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub log_to_file: bool,
    pub max_log_files: u32,
    pub max_log_size_mb: u32,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            general: GeneralConfig {
                auto_start: false,
                minimize_to_tray: true,
                check_updates: true,
                language: "zh-CN".to_string(),
            },
            automation: AutomationConfig {
                default_delay: 100,
                max_execution_time: 3600,
                enable_hotkeys: true,
                record_mouse_moves: true,
                record_window_events: false,
                screenshot_on_error: true,
            },
            ui: UIConfig {
                theme: "dark".to_string(),
                window_width: 1200,
                window_height: 800,
                window_x: 100,
                window_y: 100,
                always_on_top: false,
                show_toolbar: true,
                show_status_bar: true,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                log_to_file: true,
                max_log_files: 10,
                max_log_size_mb: 10,
            },
        }
    }
}

impl Config {
    pub fn load() -> Result<Self> {
        let config_path = get_config_path();
        
        if !std::path::Path::new(&config_path).exists() {
            let config = Self::default();
            config.save()?;
            return Ok(config);
        }
        
        let content = fs::read_to_string(&config_path)?;
        let config: Config = serde_json::from_str(&content)?;
        
        log::debug!("配置文件加载成功: {}", config_path);
        Ok(config)
    }
    
    pub fn save(&self) -> Result<()> {
        let config_path = get_config_path();
        
        // 确保配置目录存在
        if let Some(parent) = std::path::Path::new(&config_path).parent() {
            fs::create_dir_all(parent)?;
        }
        
        let content = serde_json::to_string_pretty(self)?;
        fs::write(&config_path, content)?;
        
        log::debug!("配置文件保存成功: {}", config_path);
        Ok(())
    }
    
    pub fn reset_to_default(&mut self) -> Result<()> {
        *self = Self::default();
        self.save()?;
        log::info!("配置已重置为默认值");
        Ok(())
    }
    
    pub fn validate(&self) -> Result<()> {
        // 验证延迟时间
        if self.automation.default_delay > 10000 {
            return Err(anyhow::anyhow!("默认延迟时间不能超过10秒"));
        }
        
        // 验证最大执行时间
        if self.automation.max_execution_time > 86400 {
            return Err(anyhow::anyhow!("最大执行时间不能超过24小时"));
        }
        
        // 验证窗口尺寸
        if self.ui.window_width < 800 || self.ui.window_height < 600 {
            return Err(anyhow::anyhow!("窗口尺寸不能小于800x600"));
        }
        
        // 验证日志级别
        let valid_levels = ["trace", "debug", "info", "warn", "error"];
        if !valid_levels.contains(&self.logging.level.as_str()) {
            return Err(anyhow::anyhow!("无效的日志级别: {}", self.logging.level));
        }
        
        // 验证主题
        let valid_themes = ["light", "dark", "auto"];
        if !valid_themes.contains(&self.ui.theme.as_str()) {
            return Err(anyhow::anyhow!("无效的主题: {}", self.ui.theme));
        }
        
        Ok(())
    }
    
    pub fn get_log_level(&self) -> log::LevelFilter {
        match self.logging.level.as_str() {
            "trace" => log::LevelFilter::Trace,
            "debug" => log::LevelFilter::Debug,
            "info" => log::LevelFilter::Info,
            "warn" => log::LevelFilter::Warn,
            "error" => log::LevelFilter::Error,
            _ => log::LevelFilter::Info,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_config_default() {
        let config = Config::default();
        assert_eq!(config.general.language, "zh-CN");
        assert_eq!(config.automation.default_delay, 100);
        assert_eq!(config.ui.theme, "dark");
        assert_eq!(config.logging.level, "info");
    }
    
    #[test]
    fn test_config_validation() {
        let mut config = Config::default();
        assert!(config.validate().is_ok());
        
        config.automation.default_delay = 20000;
        assert!(config.validate().is_err());
        
        config.automation.default_delay = 100;
        config.ui.window_width = 500;
        assert!(config.validate().is_err());
        
        config.ui.window_width = 1200;
        config.logging.level = "invalid".to_string();
        assert!(config.validate().is_err());
    }
    
    #[test]
    fn test_log_level_conversion() {
        let mut config = Config::default();
        
        config.logging.level = "debug".to_string();
        assert_eq!(config.get_log_level(), log::LevelFilter::Debug);
        
        config.logging.level = "error".to_string();
        assert_eq!(config.get_log_level(), log::LevelFilter::Error);
        
        config.logging.level = "invalid".to_string();
        assert_eq!(config.get_log_level(), log::LevelFilter::Info);
    }
}
