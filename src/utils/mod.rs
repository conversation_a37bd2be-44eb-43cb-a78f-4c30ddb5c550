use anyhow::Result;
use std::fs;
use std::path::Path;

pub mod hotkey;
pub mod config;

pub use hotkey::HotkeyManager;
pub use config::Config;

/// 确保目录存在，如果不存在则创建
pub fn ensure_dir_exists(path: &str) -> Result<()> {
    if !Path::new(path).exists() {
        fs::create_dir_all(path)?;
        log::debug!("创建目录: {}", path);
    }
    Ok(())
}

/// 获取应用程序数据目录
pub fn get_app_data_dir() -> String {
    #[cfg(windows)]
    {
        if let Ok(appdata) = std::env::var("APPDATA") {
            format!("{}/Acto", appdata)
        } else {
            "data".to_string()
        }
    }
    
    #[cfg(unix)]
    {
        if let Ok(home) = std::env::var("HOME") {
            format!("{}/.acto", home)
        } else {
            "data".to_string()
        }
    }
}

/// 获取配置文件路径
pub fn get_config_path() -> String {
    format!("{}/config.json", get_app_data_dir())
}

/// 获取脚本目录路径
pub fn get_scripts_dir() -> String {
    format!("{}/scripts", get_app_data_dir())
}

/// 获取日志目录路径
pub fn get_logs_dir() -> String {
    format!("{}/logs", get_app_data_dir())
}

/// 初始化应用程序目录结构
pub fn init_app_dirs() -> Result<()> {
    let app_dir = get_app_data_dir();
    ensure_dir_exists(&app_dir)?;
    ensure_dir_exists(&get_scripts_dir())?;
    ensure_dir_exists(&get_logs_dir())?;
    
    log::info!("应用程序目录初始化完成: {}", app_dir);
    Ok(())
}

/// 格式化文件大小
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// 格式化持续时间
pub fn format_duration(duration: std::time::Duration) -> String {
    let total_seconds = duration.as_secs();
    let hours = total_seconds / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;
    
    if hours > 0 {
        format!("{}h {}m {}s", hours, minutes, seconds)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, seconds)
    } else {
        format!("{}s", seconds)
    }
}

/// 验证脚本名称
pub fn validate_script_name(name: &str) -> Result<()> {
    if name.is_empty() {
        return Err(anyhow::anyhow!("脚本名称不能为空"));
    }
    
    if name.len() > 100 {
        return Err(anyhow::anyhow!("脚本名称不能超过100个字符"));
    }
    
    // 检查是否包含非法字符
    let invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
    for ch in invalid_chars.iter() {
        if name.contains(*ch) {
            return Err(anyhow::anyhow!("脚本名称不能包含字符: {}", ch));
        }
    }
    
    Ok(())
}

/// 生成安全的文件名
pub fn sanitize_filename(name: &str) -> String {
    let invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
    let mut result = String::new();
    
    for ch in name.chars() {
        if invalid_chars.contains(&ch) {
            result.push('_');
        } else {
            result.push(ch);
        }
    }
    
    // 限制长度
    if result.len() > 100 {
        result.truncate(100);
    }
    
    result
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(512), "512 B");
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1536), "1.5 KB");
        assert_eq!(format_file_size(1048576), "1.0 MB");
    }
    
    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(std::time::Duration::from_secs(30)), "30s");
        assert_eq!(format_duration(std::time::Duration::from_secs(90)), "1m 30s");
        assert_eq!(format_duration(std::time::Duration::from_secs(3661)), "1h 1m 1s");
    }
    
    #[test]
    fn test_validate_script_name() {
        assert!(validate_script_name("valid_name").is_ok());
        assert!(validate_script_name("").is_err());
        assert!(validate_script_name("name/with/slash").is_err());
        assert!(validate_script_name(&"a".repeat(101)).is_err());
    }
    
    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("valid_name"), "valid_name");
        assert_eq!(sanitize_filename("name/with\\invalid:chars"), "name_with_invalid_chars");
        assert_eq!(sanitize_filename(&"a".repeat(150)), "a".repeat(100));
    }
}
