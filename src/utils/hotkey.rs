use anyhow::Result;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub struct Hotkey {
    pub modifiers: Vec<String>,
    pub key: String,
}

impl Hotkey {
    pub fn new(modifiers: Vec<String>, key: String) -> Self {
        Self { modifiers, key }
    }

    pub fn from_string(hotkey_str: &str) -> Result<Self> {
        let parts: Vec<&str> = hotkey_str.split('+').map(|s| s.trim()).collect();

        if parts.is_empty() {
            return Err(anyhow::anyhow!("热键字符串不能为空"));
        }

        let key = parts.last().unwrap().to_string();
        let modifiers = parts[..parts.len() - 1]
            .iter()
            .map(|s| s.to_string())
            .collect();

        Ok(Self { modifiers, key })
    }

    pub fn to_string(&self) -> String {
        if self.modifiers.is_empty() {
            self.key.clone()
        } else {
            format!("{}+{}", self.modifiers.join("+"), self.key)
        }
    }
}

pub type HotkeyCallback = Box<dyn Fn() + Send + Sync>;

pub struct HotkeyManager {
    hotkeys: Arc<Mutex<HashMap<Hotkey, Arc<HotkeyCallback>>>>,
    is_listening: Arc<Mutex<bool>>,
}

impl HotkeyManager {
    pub fn new() -> Self {
        Self {
            hotkeys: Arc::new(Mutex::new(HashMap::new())),
            is_listening: Arc::new(Mutex::new(false)),
        }
    }

    pub fn register_hotkey<F>(&self, hotkey: Hotkey, callback: F) -> Result<()>
    where
        F: Fn() + Send + Sync + 'static,
    {
        let mut hotkeys = self.hotkeys.lock().unwrap();
        hotkeys.insert(hotkey.clone(), Arc::new(Box::new(callback)));

        log::info!("注册热键: {}", hotkey.to_string());

        #[cfg(windows)]
        {
            self.register_hotkey_windows(&hotkey)?;
        }

        #[cfg(target_os = "macos")]
        {
            self.register_hotkey_macos(&hotkey)?;
        }

        #[cfg(all(unix, not(target_os = "macos")))]
        {
            self.register_hotkey_x11(&hotkey)?;
        }

        Ok(())
    }

    pub fn unregister_hotkey(&self, hotkey: &Hotkey) -> Result<bool> {
        let mut hotkeys = self.hotkeys.lock().unwrap();
        let removed = hotkeys.remove(hotkey).is_some();

        if removed {
            log::info!("取消注册热键: {}", hotkey.to_string());

            #[cfg(windows)]
            {
                self.unregister_hotkey_windows(hotkey)?;
            }

            #[cfg(target_os = "macos")]
            {
                self.unregister_hotkey_macos(hotkey)?;
            }

            #[cfg(all(unix, not(target_os = "macos")))]
            {
                self.unregister_hotkey_x11(hotkey)?;
            }
        }

        Ok(removed)
    }

    pub fn start_listening(&self) -> Result<()> {
        let mut is_listening = self.is_listening.lock().unwrap();
        if *is_listening {
            return Ok(());
        }
        *is_listening = true;
        drop(is_listening);

        let hotkeys = Arc::clone(&self.hotkeys);
        let is_listening = Arc::clone(&self.is_listening);

        std::thread::spawn(move || {
            log::info!("开始监听热键");

            while *is_listening.lock().unwrap() {
                // TODO: 实现热键监听循环
                #[cfg(windows)]
                {
                    // 使用Windows消息循环监听热键
                    std::thread::sleep(std::time::Duration::from_millis(100));
                }

                #[cfg(unix)]
                {
                    // 使用X11事件循环监听热键
                    std::thread::sleep(std::time::Duration::from_millis(100));
                }
            }

            log::info!("停止监听热键");
        });

        Ok(())
    }

    pub fn stop_listening(&self) -> Result<()> {
        let mut is_listening = self.is_listening.lock().unwrap();
        *is_listening = false;
        log::info!("热键监听已停止");
        Ok(())
    }

    pub fn list_hotkeys(&self) -> Vec<String> {
        let hotkeys = self.hotkeys.lock().unwrap();
        hotkeys.keys().map(|h| h.to_string()).collect()
    }

    fn handle_hotkey_pressed(&self, hotkey: &Hotkey) {
        let hotkeys = self.hotkeys.lock().unwrap();
        if let Some(callback) = hotkeys.get(hotkey) {
            log::debug!("热键触发: {}", hotkey.to_string());
            callback();
        }
    }

    #[cfg(windows)]
    fn register_hotkey_windows(&self, hotkey: &Hotkey) -> Result<()> {
        use windows::Win32::Foundation::HWND;
        use windows::Win32::UI::Input::KeyboardAndMouse::{
            MOD_ALT, MOD_CONTROL, MOD_SHIFT, MOD_WIN, RegisterHotKey,
        };

        let mut modifiers = 0u32;
        for modifier in &hotkey.modifiers {
            match modifier.as_str() {
                "ctrl" | "control" => modifiers |= MOD_CONTROL.0,
                "alt" => modifiers |= MOD_ALT.0,
                "shift" => modifiers |= MOD_SHIFT.0,
                "win" | "windows" => modifiers |= MOD_WIN.0,
                _ => log::warn!("未知的修饰键: {}", modifier),
            }
        }

        // 简单的键码映射
        let vk_code = match hotkey.key.to_lowercase().as_str() {
            "f1" => 0x70,
            "f2" => 0x71,
            "f3" => 0x72,
            "f4" => 0x73,
            "f5" => 0x74,
            "f6" => 0x75,
            "f7" => 0x76,
            "f8" => 0x77,
            "f9" => 0x78,
            "f10" => 0x79,
            "f11" => 0x7A,
            "f12" => 0x7B,
            _ => {
                if hotkey.key.len() == 1 {
                    hotkey.key.chars().next().unwrap() as u32
                } else {
                    return Err(anyhow::anyhow!("不支持的热键: {}", hotkey.key));
                }
            }
        };

        let hotkey_id = (hotkey.key.as_ptr() as usize) as i32; // 简单的ID生成

        unsafe {
            if RegisterHotKey(HWND::default(), hotkey_id, modifiers, vk_code).as_bool() {
                log::debug!("Windows热键注册成功: {}", hotkey.to_string());
                Ok(())
            } else {
                Err(anyhow::anyhow!(
                    "Windows热键注册失败: {}",
                    hotkey.to_string()
                ))
            }
        }
    }

    #[cfg(windows)]
    fn unregister_hotkey_windows(&self, hotkey: &Hotkey) -> Result<()> {
        use windows::Win32::Foundation::HWND;
        use windows::Win32::UI::Input::KeyboardAndMouse::UnregisterHotKey;

        let hotkey_id = (hotkey.key.as_ptr() as usize) as i32;

        unsafe {
            if UnregisterHotKey(HWND::default(), hotkey_id).as_bool() {
                log::debug!("Windows热键取消注册成功: {}", hotkey.to_string());
                Ok(())
            } else {
                Err(anyhow::anyhow!(
                    "Windows热键取消注册失败: {}",
                    hotkey.to_string()
                ))
            }
        }
    }

    #[cfg(target_os = "macos")]
    fn register_hotkey_macos(&self, hotkey: &Hotkey) -> Result<()> {
        // TODO: 实现macOS热键注册
        log::debug!("macOS热键注册: {}", hotkey.to_string());
        Ok(())
    }

    #[cfg(target_os = "macos")]
    fn unregister_hotkey_macos(&self, hotkey: &Hotkey) -> Result<()> {
        // TODO: 实现macOS热键取消注册
        log::debug!("macOS热键取消注册: {}", hotkey.to_string());
        Ok(())
    }

    #[cfg(all(unix, not(target_os = "macos")))]
    fn register_hotkey_x11(&self, hotkey: &Hotkey) -> Result<()> {
        // TODO: 实现X11热键注册
        log::debug!("X11热键注册: {}", hotkey.to_string());
        Ok(())
    }

    #[cfg(all(unix, not(target_os = "macos")))]
    fn unregister_hotkey_x11(&self, hotkey: &Hotkey) -> Result<()> {
        // TODO: 实现X11热键取消注册
        log::debug!("X11热键取消注册: {}", hotkey.to_string());
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hotkey_from_string() {
        let hotkey = Hotkey::from_string("Ctrl+Alt+F1").unwrap();
        assert_eq!(hotkey.modifiers, vec!["Ctrl", "Alt"]);
        assert_eq!(hotkey.key, "F1");

        let hotkey = Hotkey::from_string("F2").unwrap();
        assert!(hotkey.modifiers.is_empty());
        assert_eq!(hotkey.key, "F2");
    }

    #[test]
    fn test_hotkey_to_string() {
        let hotkey = Hotkey::new(
            vec!["Ctrl".to_string(), "Alt".to_string()],
            "F1".to_string(),
        );
        assert_eq!(hotkey.to_string(), "Ctrl+Alt+F1");

        let hotkey = Hotkey::new(vec![], "F2".to_string());
        assert_eq!(hotkey.to_string(), "F2");
    }
}
