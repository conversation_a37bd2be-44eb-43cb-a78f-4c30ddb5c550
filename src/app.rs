use crate::automation::AutomationEngine;
use crate::script::ScriptManager;
use crate::task::TaskScheduler;
use makepad_widgets::*;

live_design! {
    use link::theme::*;
    use link::shaders::*;
    use link::widgets::*;

    App = {{App}} {
        ui: <Window> {
            window: {inner_size: vec2(800, 600)},
            pass: {clear_color: #2b2b2b},

            body = <View> {
                flow: Down,
                spacing: 30,
                align: {x: 0.5, y: 0.5},

                title_label = <Label> {
                    text: "Acto 桌面自动化软件"
                    draw_text: {color: #fff, text_style: {font_size: 24}}
                }

                record_button = <Button> {
                    text: "开始录制"
                    draw_text: {color: #fff, text_style: {font_size: 18}}
                }

                stop_button = <Button> {
                    text: "停止录制"
                    draw_text: {color: #fff, text_style: {font_size: 18}}
                }

                play_button = <Button> {
                    text: "播放脚本"
                    draw_text: {color: #fff, text_style: {font_size: 18}}
                }

                script_list_label = <Label> {
                    text: "录制的脚本列表:"
                    draw_text: {color: #fff, text_style: {font_size: 16}}
                }

                script_list_scroll = <ScrollXYView> {
                    height: 200,
                    width: Fill,
                    show_bg: true,
                    draw_bg: {color: #1e1e1e}

                    script_list_view = <View> {
                        flow: Down,
                        spacing: 5,
                        padding: 10

                        empty_label = <Label> {
                            text: "暂无录制的脚本\n点击'开始录制'来录制新脚本",
                            draw_text: {
                                color: #ccc,
                                text_style: {font_size: 12},
                                wrap: Word
                            }
                        }
                    }
                }

                script_controls = <View> {
                    flow: Right,
                    spacing: 10

                    refresh_button = <Button> {
                        text: "刷新列表"
                        draw_text: {color: #fff, text_style: {font_size: 14}}
                    }

                    prev_script_button = <Button> {
                        text: "上一个"
                        draw_text: {color: #fff, text_style: {font_size: 14}}
                    }

                    next_script_button = <Button> {
                        text: "下一个"
                        draw_text: {color: #fff, text_style: {font_size: 14}}
                    }
                }
            }
        }
    }
}

#[derive(Live, LiveHook)]
pub struct App {
    #[live]
    ui: WidgetRef,
    #[rust]
    automation_engine: AutomationEngine,
    #[rust]
    script_manager: ScriptManager,
    #[rust]
    task_scheduler: TaskScheduler,
    #[rust]
    selected_script_id: Option<String>,
}

impl LiveRegister for App {
    fn live_register(cx: &mut Cx) {
        makepad_widgets::live_design(cx);
    }
}

impl AppMain for App {
    fn handle_event(&mut self, cx: &mut Cx, event: &Event) {
        self.match_event(cx, event);
        let mut scope = Scope::empty();
        self.ui.handle_event(cx, event, &mut scope);

        // 在第一次事件处理时初始化脚本列表
        static mut INITIALIZED: bool = false;
        unsafe {
            if !INITIALIZED {
                self.update_script_list(cx);
                INITIALIZED = true;
            }
        }
    }
}

impl App {
    pub fn new() -> Self {
        Self {
            ui: WidgetRef::default(),
            automation_engine: AutomationEngine::new(),
            script_manager: ScriptManager::new(),
            task_scheduler: TaskScheduler::new(),
            selected_script_id: None,
        }
    }

    fn update_script_list(&mut self, cx: &mut Cx) {
        let scripts = self.script_manager.list_scripts();

        // 在日志中显示脚本列表
        ::log::info!("=== 脚本列表 ===");
        if scripts.is_empty() {
            ::log::info!("暂无录制的脚本");
            // 显示空状态
            self.ui
                .label(id!(empty_label))
                .set_text(cx, "暂无录制的脚本\n点击'开始录制'来录制新脚本");
        } else {
            // 构建脚本列表文本
            let mut list_text = format!("共有 {} 个录制的脚本:\n\n", scripts.len());

            for (i, script) in scripts.iter().enumerate() {
                let created_time = script.created_at.format("%m-%d %H:%M");
                let is_selected = self.selected_script_id.as_ref() == Some(&script.id);
                let selection_indicator = if is_selected { "► " } else { "  " };

                list_text.push_str(&format!(
                    "{}{}. {}\n   {} 个动作 | {}\n\n",
                    selection_indicator,
                    i + 1,
                    script.name,
                    script.actions.len(),
                    created_time
                ));

                ::log::info!(
                    "{}{}. {} ({} 动作) - 创建时间: {}",
                    selection_indicator,
                    i + 1,
                    script.name,
                    script.actions.len(),
                    script.created_at.format("%Y-%m-%d %H:%M:%S")
                );
            }

            // 更新UI显示脚本列表
            self.ui.label(id!(empty_label)).set_text(cx, &list_text);
        }
        ::log::info!("===============");
        ::log::info!("脚本列表已更新，共 {} 个脚本", scripts.len());

        // 如果没有选中的脚本且有脚本存在，选中第一个
        if self.selected_script_id.is_none() && !scripts.is_empty() {
            self.selected_script_id = Some(scripts[0].id.clone());
            ::log::info!("自动选中第一个脚本: {}", scripts[0].name);
        }
    }

    fn select_previous_script(&mut self) {
        let scripts = self.script_manager.list_scripts();
        if scripts.is_empty() {
            return;
        }

        if let Some(current_id) = &self.selected_script_id {
            if let Some(current_index) = scripts.iter().position(|s| &s.id == current_id) {
                let prev_index = if current_index == 0 {
                    scripts.len() - 1 // 循环到最后一个
                } else {
                    current_index - 1
                };
                self.selected_script_id = Some(scripts[prev_index].id.clone());
                ::log::info!("选中上一个脚本: {}", scripts[prev_index].name);
            }
        }
    }

    fn select_next_script(&mut self) {
        let scripts = self.script_manager.list_scripts();
        if scripts.is_empty() {
            return;
        }

        if let Some(current_id) = &self.selected_script_id {
            if let Some(current_index) = scripts.iter().position(|s| &s.id == current_id) {
                let next_index = if current_index == scripts.len() - 1 {
                    0 // 循环到第一个
                } else {
                    current_index + 1
                };
                self.selected_script_id = Some(scripts[next_index].id.clone());
                ::log::info!("选中下一个脚本: {}", scripts[next_index].name);
            }
        }
    }
}

impl MatchEvent for App {
    fn handle_actions(&mut self, cx: &mut Cx, actions: &Actions) {
        // 处理UI事件，将事件传递给各个组件
        // 这里我们直接处理按钮事件，因为我们的UI结构比较简单

        // 处理录制按钮点击
        if self.ui.button(id!(record_button)).clicked(actions) {
            ::log::info!("开始录制脚本");
            if let Err(e) = self.automation_engine.start_recording() {
                ::log::error!("启动录制失败: {}", e);
            } else {
                cx.redraw_all(); // 刷新UI状态
            }
        }

        // 处理停止录制按钮点击
        if self.ui.button(id!(stop_button)).clicked(actions) {
            ::log::info!("停止录制脚本");
            match self.automation_engine.stop_recording() {
                Ok(recorded_actions) => {
                    ::log::info!("录制完成，共录制 {} 个动作", recorded_actions.len());

                    // 创建新脚本并保存录制的动作
                    let script_name =
                        format!("录制脚本_{}", chrono::Utc::now().format("%Y%m%d_%H%M%S"));
                    match self
                        .script_manager
                        .create_script(script_name, "自动录制的脚本".to_string())
                    {
                        Ok(script_id) => {
                            if let Some(script) = self.script_manager.get_script_mut(&script_id) {
                                for action in recorded_actions {
                                    script.add_action(action);
                                }
                                if let Err(e) = self.script_manager.save_script(&script_id) {
                                    ::log::error!("保存脚本失败: {}", e);
                                } else {
                                    ::log::info!("脚本已保存，ID: {}", script_id);
                                }
                            }
                        }
                        Err(e) => {
                            ::log::error!("创建脚本失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    ::log::error!("停止录制失败: {}", e);
                }
            }
            // 刷新脚本列表
            self.update_script_list(cx);
            cx.redraw_all();
        }

        // 处理刷新按钮点击
        if self.ui.button(id!(refresh_button)).clicked(actions) {
            ::log::info!("刷新脚本列表");
            self.update_script_list(cx);
            cx.redraw_all();
        }

        // 处理上一个脚本按钮
        if self.ui.button(id!(prev_script_button)).clicked(actions) {
            self.select_previous_script();
            self.update_script_list(cx);
            cx.redraw_all();
        }

        // 处理下一个脚本按钮
        if self.ui.button(id!(next_script_button)).clicked(actions) {
            self.select_next_script();
            self.update_script_list(cx);
            cx.redraw_all();
        }

        // 处理播放脚本按钮点击
        if self.ui.button(id!(play_button)).clicked(actions) {
            ::log::info!("播放脚本");

            // 优先播放选中的脚本
            if let Some(selected_id) = &self.selected_script_id {
                if let Some(script) = self.script_manager.get_script(selected_id) {
                    ::log::info!("播放选中的脚本: {}", script.name);
                    if let Err(e) = self.automation_engine.execute_script(&script.actions) {
                        ::log::error!("执行脚本失败: {}", e);
                    } else {
                        ::log::info!("脚本执行完成");
                    }
                    return;
                }
            }

            // 如果没有选中脚本，尝试播放最新录制的动作
            let recorded_actions = self.automation_engine.get_recorded_actions();
            if !recorded_actions.is_empty() {
                ::log::info!("播放最新录制的动作");
                if let Err(e) = self.automation_engine.execute_script(&recorded_actions) {
                    ::log::error!("执行脚本失败: {}", e);
                } else {
                    ::log::info!("脚本执行完成");
                }
            } else {
                // 如果都没有，尝试播放第一个脚本
                let scripts = self.script_manager.list_scripts();
                if let Some(script) = scripts.first() {
                    ::log::info!("播放第一个脚本: {}", script.name);
                    if let Err(e) = self.automation_engine.execute_script(&script.actions) {
                        ::log::error!("执行脚本失败: {}", e);
                    } else {
                        ::log::info!("脚本执行完成");
                    }
                } else {
                    ::log::warn!("没有可播放的脚本");
                }
            }
        }
    }
}

app_main!(App);
