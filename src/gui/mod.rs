use crate::automation::AutomationEngine;
use crate::script::ScriptManager;
use crate::task::TaskScheduler;
use makepad_widgets::*;

live_design! {
    use link::theme::*;
    use link::shaders::*;
    use link::widgets::*;

    pub AppUI = {{AppUI}} {
        width: Fill,
        height: Fill,

        show_bg: true,
        draw_bg: {
            color: #2b2b2b
        }

        <View> {
            width: Fill,
            height: Fill,

            <Label> {
                text: "Acto 桌面自动化软件",
                margin: {top: 20, left: 20}
            }

            <Button> {
                text: "开始录制"
                margin: {top: 60, left: 20}
                width: 120,
                height: 40,
            }

            <Button> {
                text: "停止录制"
                margin: {top: 110, left: 20}
                width: 120,
                height: 40,
            }

            <Button> {
                text: "播放脚本"
                margin: {top: 160, left: 20}
                width: 120,
                height: 40,
            }
        }
    }
}

#[derive(Live, LiveHook, Widget)]
pub struct AppUI {
    #[deref]
    view: View,
}

impl Default for AppUI {
    fn default() -> Self {
        // 这个实现是临时的，实际的初始化会在Live系统中完成
        unsafe { std::mem::zeroed() }
    }
}

impl Widget for AppUI {
    fn handle_event(&mut self, cx: &mut Cx, event: &Event, scope: &mut Scope) {
        self.view.handle_event(cx, event, scope);
    }

    fn draw_walk(&mut self, cx: &mut Cx2d, scope: &mut Scope, walk: Walk) -> DrawStep {
        self.view.draw_walk(cx, scope, walk)
    }
}

impl AppUI {
    pub fn handle_actions(
        &mut self,
        cx: &mut Cx,
        actions: &Actions,
        automation_engine: &mut AutomationEngine,
        script_manager: &mut ScriptManager,
        _task_scheduler: &mut TaskScheduler,
    ) {
        // 处理录制按钮点击
        if self.view.button(id!(record_button)).clicked(actions) {
            ::log::info!("开始录制脚本");
            if let Err(e) = automation_engine.start_recording() {
                ::log::error!("启动录制失败: {}", e);
            } else {
                cx.redraw_all(); // 刷新UI状态
            }
        }

        // 处理停止录制按钮点击
        if self.view.button(id!(stop_button)).clicked(actions) {
            ::log::info!("停止录制脚本");
            match automation_engine.stop_recording() {
                Ok(recorded_actions) => {
                    ::log::info!("录制完成，共录制 {} 个动作", recorded_actions.len());

                    // 创建新脚本并保存录制的动作
                    let script_name =
                        format!("录制脚本_{}", chrono::Utc::now().format("%Y%m%d_%H%M%S"));
                    match script_manager.create_script(script_name, "自动录制的脚本".to_string())
                    {
                        Ok(script_id) => {
                            if let Some(script) = script_manager.get_script_mut(&script_id) {
                                for action in recorded_actions {
                                    script.add_action(action);
                                }
                                if let Err(e) = script_manager.save_script(&script_id) {
                                    ::log::error!("保存脚本失败: {}", e);
                                } else {
                                    ::log::info!("脚本已保存，ID: {}", script_id);
                                }
                            }
                        }
                        Err(e) => {
                            ::log::error!("创建脚本失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    ::log::error!("停止录制失败: {}", e);
                }
            }
            cx.redraw_all();
        }

        // 处理播放脚本按钮点击
        if self.view.button(id!(play_button)).clicked(actions) {
            ::log::info!("播放脚本");
            let recorded_actions = automation_engine.get_recorded_actions().to_vec();
            if !recorded_actions.is_empty() {
                if let Err(e) = automation_engine.execute_script(&recorded_actions) {
                    ::log::error!("执行脚本失败: {}", e);
                } else {
                    ::log::info!("脚本执行完成");
                }
            } else {
                // 如果没有录制的动作，尝试播放最近的脚本
                let scripts = script_manager.list_scripts();
                if let Some(script) = scripts.first() {
                    ::log::info!("播放脚本: {}", script.name);
                    if let Err(e) = automation_engine.execute_script(&script.actions) {
                        ::log::error!("执行脚本失败: {}", e);
                    } else {
                        ::log::info!("脚本执行完成");
                    }
                } else {
                    ::log::warn!("没有可播放的脚本");
                }
            }
        }
    }
}

pub fn register_live_design(_cx: &mut Cx) {
    live_design!(cx);
}
