use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::time::Duration;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum TaskTrigger {
    // 时间触发器
    Once {
        at: DateTime<Utc>,
    },
    Interval {
        interval: Duration,
        next_run: DateTime<Utc>,
    },
    Cron {
        expression: String,
        next_run: DateTime<Utc>,
    },

    // 事件触发器
    Hotkey {
        keys: Vec<String>,
    },
    FileChange {
        path: String,
        event_type: FileEventType,
    },
    WindowEvent {
        window_title: String,
        event_type: WindowEventType,
    },

    // 系统触发器
    SystemStartup,
    UserLogin,
    IdleTime {
        minutes: u32,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileEventType {
    Created,
    Modified,
    Deleted,
    Renamed,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum WindowEventType {
    Opened,
    Closed,
    Focused,
    Minimized,
    Maximized,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum TaskStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
    Paused,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Task {
    pub id: String,
    pub name: String,
    pub description: String,
    pub script_id: String,
    pub trigger: TaskTrigger,
    pub status: TaskStatus,
    pub enabled: bool,
    pub created_at: DateTime<Utc>,
    pub last_run: Option<DateTime<Utc>>,
    pub next_run: Option<DateTime<Utc>>,
    pub run_count: u32,
    pub max_runs: Option<u32>,
    pub timeout: Option<Duration>,
    pub retry_count: u32,
    pub max_retries: u32,
}

impl Task {
    pub fn new(name: String, description: String, script_id: String, trigger: TaskTrigger) -> Self {
        let now = Utc::now();
        let next_run = Self::calculate_next_run(&trigger, now);

        Self {
            id: uuid::Uuid::new_v4().to_string(),
            name,
            description,
            script_id,
            trigger,
            status: TaskStatus::Pending,
            enabled: true,
            created_at: now,
            last_run: None,
            next_run,
            run_count: 0,
            max_runs: None,
            timeout: None,
            retry_count: 0,
            max_retries: 3,
        }
    }

    fn calculate_next_run(trigger: &TaskTrigger, from: DateTime<Utc>) -> Option<DateTime<Utc>> {
        match trigger {
            TaskTrigger::Once { at } => Some(*at),
            TaskTrigger::Interval { next_run, .. } => Some(*next_run),
            TaskTrigger::Cron { next_run, .. } => Some(*next_run),
            _ => None,
        }
    }

    pub fn should_run(&self, now: DateTime<Utc>) -> bool {
        if !self.enabled || self.status == TaskStatus::Running {
            return false;
        }

        if let Some(max_runs) = self.max_runs {
            if self.run_count >= max_runs {
                return false;
            }
        }

        match &self.trigger {
            TaskTrigger::Once { at } => now >= *at && self.run_count == 0,
            TaskTrigger::Interval { next_run, .. } => now >= *next_run,
            TaskTrigger::Cron { next_run, .. } => now >= *next_run,
            _ => false,
        }
    }

    pub fn update_next_run(&mut self, now: DateTime<Utc>) {
        match &mut self.trigger {
            TaskTrigger::Interval { interval, next_run } => {
                *next_run = now + chrono::Duration::from_std(*interval).unwrap_or_default();
                self.next_run = Some(*next_run);
            }
            TaskTrigger::Cron { next_run, .. } => {
                // TODO: 实现cron表达式解析
                *next_run = now + chrono::Duration::hours(24);
                self.next_run = Some(*next_run);
            }
            _ => {}
        }
    }
}

#[derive(Debug, Clone)]
pub struct TaskExecution {
    pub task_id: String,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub status: TaskStatus,
    pub error_message: Option<String>,
    pub output: Option<String>,
}

pub struct TaskScheduler {
    tasks: Arc<Mutex<HashMap<String, Task>>>,
    executions: Arc<Mutex<Vec<TaskExecution>>>,
    running_tasks: Arc<Mutex<HashMap<String, tokio::task::JoinHandle<()>>>>,
    is_running: Arc<Mutex<bool>>,
}

impl Default for TaskScheduler {
    fn default() -> Self {
        Self::new()
    }
}

impl TaskScheduler {
    pub fn new() -> Self {
        Self {
            tasks: Arc::new(Mutex::new(HashMap::new())),
            executions: Arc::new(Mutex::new(Vec::new())),
            running_tasks: Arc::new(Mutex::new(HashMap::new())),
            is_running: Arc::new(Mutex::new(false)),
        }
    }

    pub fn add_task(&self, task: Task) -> Result<()> {
        let mut tasks = self.tasks.lock().unwrap();
        let id = task.id.clone();
        tasks.insert(id.clone(), task);
        log::info!("添加任务: {}", id);
        Ok(())
    }

    pub fn remove_task(&self, task_id: &str) -> Result<bool> {
        let mut tasks = self.tasks.lock().unwrap();
        let removed = tasks.remove(task_id).is_some();

        if removed {
            // 停止正在运行的任务
            let mut running_tasks = self.running_tasks.lock().unwrap();
            if let Some(handle) = running_tasks.remove(task_id) {
                handle.abort();
            }
            log::info!("移除任务: {}", task_id);
        }

        Ok(removed)
    }

    pub fn get_task(&self, task_id: &str) -> Option<Task> {
        let tasks = self.tasks.lock().unwrap();
        tasks.get(task_id).cloned()
    }

    pub fn list_tasks(&self) -> Vec<Task> {
        let tasks = self.tasks.lock().unwrap();
        tasks.values().cloned().collect()
    }

    pub fn enable_task(&self, task_id: &str, enabled: bool) -> Result<bool> {
        let mut tasks = self.tasks.lock().unwrap();
        if let Some(task) = tasks.get_mut(task_id) {
            task.enabled = enabled;
            log::info!(
                "任务 {} 状态更新为: {}",
                task_id,
                if enabled { "启用" } else { "禁用" }
            );
            Ok(true)
        } else {
            Ok(false)
        }
    }

    pub fn start_scheduler(&self) -> Result<()> {
        let mut is_running = self.is_running.lock().unwrap();
        if *is_running {
            return Ok(());
        }
        *is_running = true;
        drop(is_running);

        let tasks = Arc::clone(&self.tasks);
        let executions = Arc::clone(&self.executions);
        let running_tasks = Arc::clone(&self.running_tasks);
        let is_running = Arc::clone(&self.is_running);

        tokio::spawn(async move {
            log::info!("任务调度器启动");

            while *is_running.lock().unwrap() {
                let now = Utc::now();
                let mut tasks_to_run = Vec::new();

                // 检查需要运行的任务
                {
                    let mut tasks_guard = tasks.lock().unwrap();
                    for (id, task) in tasks_guard.iter_mut() {
                        if task.should_run(now) {
                            task.status = TaskStatus::Running;
                            task.last_run = Some(now);
                            task.run_count += 1;
                            task.update_next_run(now);
                            tasks_to_run.push((id.clone(), task.clone()));
                        }
                    }
                }

                // 执行任务
                for (task_id, task) in tasks_to_run {
                    let task_id_clone = task_id.clone();
                    let executions_clone = Arc::clone(&executions);
                    let tasks_clone = Arc::clone(&tasks);
                    let running_tasks_clone = Arc::clone(&running_tasks);

                    let handle = tokio::spawn(async move {
                        let execution = TaskExecution {
                            task_id: task_id_clone.clone(),
                            started_at: Utc::now(),
                            completed_at: None,
                            status: TaskStatus::Running,
                            error_message: None,
                            output: None,
                        };

                        {
                            let mut executions_guard = executions_clone.lock().unwrap();
                            executions_guard.push(execution);
                        }

                        // TODO: 实际执行脚本
                        log::info!("执行任务: {} (脚本: {})", task.name, task.script_id);

                        // 模拟任务执行
                        tokio::time::sleep(Duration::from_secs(1)).await;

                        // 更新任务状态
                        {
                            let mut tasks_guard = tasks_clone.lock().unwrap();
                            if let Some(task) = tasks_guard.get_mut(&task_id_clone) {
                                task.status = TaskStatus::Completed;
                            }
                        }

                        // 更新执行记录
                        {
                            let mut executions_guard = executions_clone.lock().unwrap();
                            if let Some(execution) = executions_guard.last_mut() {
                                execution.completed_at = Some(Utc::now());
                                execution.status = TaskStatus::Completed;
                            }
                        }

                        // 从运行任务列表中移除
                        {
                            let mut running_tasks_guard = running_tasks_clone.lock().unwrap();
                            running_tasks_guard.remove(&task_id_clone);
                        }

                        log::info!("任务执行完成: {}", task_id_clone);
                    });

                    {
                        let mut running_tasks_guard = running_tasks.lock().unwrap();
                        running_tasks_guard.insert(task_id, handle);
                    }
                }

                // 等待一段时间再检查
                tokio::time::sleep(Duration::from_secs(1)).await;
            }

            log::info!("任务调度器停止");
        });

        Ok(())
    }

    pub fn stop_scheduler(&self) -> Result<()> {
        let mut is_running = self.is_running.lock().unwrap();
        *is_running = false;

        // 停止所有正在运行的任务
        let mut running_tasks = self.running_tasks.lock().unwrap();
        for (_, handle) in running_tasks.drain() {
            handle.abort();
        }

        log::info!("任务调度器已停止");
        Ok(())
    }

    pub fn get_executions(&self, task_id: Option<&str>) -> Vec<TaskExecution> {
        let executions = self.executions.lock().unwrap();
        match task_id {
            Some(id) => executions
                .iter()
                .filter(|e| e.task_id == id)
                .cloned()
                .collect(),
            None => executions.clone(),
        }
    }
}
