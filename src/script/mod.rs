use crate::automation::AutomationAction;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Script {
    pub id: String,
    pub name: String,
    pub description: String,
    pub actions: Vec<AutomationAction>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub modified_at: chrono::DateTime<chrono::Utc>,
    pub tags: Vec<String>,
}

impl Script {
    pub fn new(name: String, description: String) -> Self {
        let now = chrono::Utc::now();
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            name,
            description,
            actions: Vec::new(),
            created_at: now,
            modified_at: now,
            tags: Vec::new(),
        }
    }

    pub fn add_action(&mut self, action: AutomationAction) {
        self.actions.push(action);
        self.modified_at = chrono::Utc::now();
    }

    pub fn remove_action(&mut self, index: usize) -> Option<AutomationAction> {
        if index < self.actions.len() {
            self.modified_at = chrono::Utc::now();
            Some(self.actions.remove(index))
        } else {
            None
        }
    }

    pub fn insert_action(&mut self, index: usize, action: AutomationAction) {
        if index <= self.actions.len() {
            self.actions.insert(index, action);
            self.modified_at = chrono::Utc::now();
        }
    }

    pub fn update_action(&mut self, index: usize, action: AutomationAction) -> bool {
        if index < self.actions.len() {
            self.actions[index] = action;
            self.modified_at = chrono::Utc::now();
            true
        } else {
            false
        }
    }

    pub fn add_tag(&mut self, tag: String) {
        if !self.tags.contains(&tag) {
            self.tags.push(tag);
            self.modified_at = chrono::Utc::now();
        }
    }

    pub fn remove_tag(&mut self, tag: &str) -> bool {
        if let Some(pos) = self.tags.iter().position(|t| t == tag) {
            self.tags.remove(pos);
            self.modified_at = chrono::Utc::now();
            true
        } else {
            false
        }
    }
}

pub struct ScriptManager {
    scripts: HashMap<String, Script>,
    scripts_dir: String,
}

impl Default for ScriptManager {
    fn default() -> Self {
        Self::new()
    }
}

impl ScriptManager {
    pub fn new() -> Self {
        let scripts_dir = "scripts".to_string();

        // 创建脚本目录
        if let Err(e) = fs::create_dir_all(&scripts_dir) {
            log::warn!("无法创建脚本目录: {}", e);
        }

        let mut manager = Self {
            scripts: HashMap::new(),
            scripts_dir,
        };

        // 加载现有脚本
        if let Err(e) = manager.load_scripts() {
            log::warn!("加载脚本失败: {}", e);
        }

        manager
    }

    pub fn create_script(&mut self, name: String, description: String) -> Result<String> {
        let script = Script::new(name, description);
        let id = script.id.clone();

        self.scripts.insert(id.clone(), script);
        self.save_script(&id)?;

        log::info!("创建新脚本: {}", id);
        Ok(id)
    }

    pub fn get_script(&self, id: &str) -> Option<&Script> {
        self.scripts.get(id)
    }

    pub fn get_script_mut(&mut self, id: &str) -> Option<&mut Script> {
        self.scripts.get_mut(id)
    }

    pub fn delete_script(&mut self, id: &str) -> Result<bool> {
        if self.scripts.remove(id).is_some() {
            let file_path = format!("{}/{}.json", self.scripts_dir, id);
            if Path::new(&file_path).exists() {
                fs::remove_file(file_path)?;
            }
            log::info!("删除脚本: {}", id);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    pub fn list_scripts(&self) -> Vec<&Script> {
        self.scripts.values().collect()
    }

    pub fn search_scripts(&self, query: &str) -> Vec<&Script> {
        let query = query.to_lowercase();
        self.scripts
            .values()
            .filter(|script| {
                script.name.to_lowercase().contains(&query)
                    || script.description.to_lowercase().contains(&query)
                    || script
                        .tags
                        .iter()
                        .any(|tag| tag.to_lowercase().contains(&query))
            })
            .collect()
    }

    pub fn save_script(&self, id: &str) -> Result<()> {
        if let Some(script) = self.scripts.get(id) {
            let file_path = format!("{}/{}.json", self.scripts_dir, id);
            let json = serde_json::to_string_pretty(script)?;
            fs::write(file_path, json)?;
            log::debug!("保存脚本: {}", id);
        }
        Ok(())
    }

    pub fn load_script(&mut self, file_path: &str) -> Result<String> {
        let content = fs::read_to_string(file_path)?;
        let script: Script = serde_json::from_str(&content)?;
        let id = script.id.clone();

        self.scripts.insert(id.clone(), script);
        log::debug!("加载脚本: {}", id);
        Ok(id)
    }

    pub fn load_scripts(&mut self) -> Result<()> {
        if !Path::new(&self.scripts_dir).exists() {
            return Ok(());
        }

        let entries = fs::read_dir(&self.scripts_dir)?;
        for entry in entries {
            let entry = entry?;
            let path = entry.path();

            if path.extension().and_then(|s| s.to_str()) == Some("json") {
                if let Err(e) = self.load_script(path.to_str().unwrap()) {
                    log::warn!("加载脚本文件失败 {}: {}", path.display(), e);
                }
            }
        }

        log::info!("加载了 {} 个脚本", self.scripts.len());
        Ok(())
    }

    pub fn export_script(&self, id: &str, export_path: &str) -> Result<()> {
        if let Some(script) = self.scripts.get(id) {
            let json = serde_json::to_string_pretty(script)?;
            fs::write(export_path, json)?;
            log::info!("导出脚本 {} 到 {}", id, export_path);
        }
        Ok(())
    }

    pub fn import_script(&mut self, import_path: &str) -> Result<String> {
        let content = fs::read_to_string(import_path)?;
        let mut script: Script = serde_json::from_str(&content)?;

        // 生成新的ID以避免冲突
        script.id = uuid::Uuid::new_v4().to_string();
        script.modified_at = chrono::Utc::now();

        let id = script.id.clone();
        self.scripts.insert(id.clone(), script);
        self.save_script(&id)?;

        log::info!("导入脚本: {}", id);
        Ok(id)
    }
}
