pub mod input;
pub mod window;

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::time::Duration;

pub use input::InputController;
pub use window::WindowManager;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AutomationAction {
    // 鼠标操作
    MouseMove {
        x: i32,
        y: i32,
    },
    MouseClick {
        button: MouseButton,
        x: i32,
        y: i32,
    },
    MouseDoubleClick {
        button: MouseButton,
        x: i32,
        y: i32,
    },
    MouseDrag {
        from_x: i32,
        from_y: i32,
        to_x: i32,
        to_y: i32,
    },
    MouseScroll {
        x: i32,
        y: i32,
        delta: i32,
    },

    // 键盘操作
    KeyPress {
        key: String,
    },
    KeyRelease {
        key: String,
    },
    KeyCombo {
        keys: Vec<String>,
    },
    TypeText {
        text: String,
    },

    // 窗口操作
    WindowFocus {
        title: String,
    },
    WindowMove {
        title: String,
        x: i32,
        y: i32,
    },
    WindowResize {
        title: String,
        width: i32,
        height: i32,
    },
    WindowMaximize {
        title: String,
    },
    WindowMinimize {
        title: String,
    },
    WindowClose {
        title: String,
    },

    // 控制操作
    Wait {
        duration: Duration,
    },
    Screenshot {
        path: String,
    },

    // 条件和循环
    If {
        condition: String,
        actions: Vec<AutomationAction>,
    },
    Loop {
        count: u32,
        actions: Vec<AutomationAction>,
    },
    While {
        condition: String,
        actions: Vec<AutomationAction>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MouseButton {
    Left,
    Right,
    Middle,
}

pub struct AutomationEngine {
    input_controller: InputController,
    window_manager: WindowManager,
    is_recording: bool,
    recorded_actions: Vec<AutomationAction>,
}

impl Default for AutomationEngine {
    fn default() -> Self {
        Self::new()
    }
}

impl AutomationEngine {
    pub fn new() -> Self {
        Self {
            input_controller: InputController::new(),
            window_manager: WindowManager::new(),
            is_recording: false,
            recorded_actions: Vec::new(),
        }
    }

    pub fn start_recording(&mut self) -> Result<()> {
        self.is_recording = true;
        self.recorded_actions.clear();
        log::info!("开始录制自动化脚本");
        Ok(())
    }

    pub fn stop_recording(&mut self) -> Result<Vec<AutomationAction>> {
        self.is_recording = false;
        log::info!("停止录制，共录制了 {} 个动作", self.recorded_actions.len());
        Ok(self.recorded_actions.clone())
    }

    pub fn execute_action(&mut self, action: &AutomationAction) -> Result<()> {
        match action {
            AutomationAction::MouseMove { x, y } => {
                self.input_controller.move_mouse(*x, *y)?;
            }
            AutomationAction::MouseClick { button, x, y } => {
                self.input_controller.move_mouse(*x, *y)?;
                self.input_controller.click_mouse(button)?;
            }
            AutomationAction::MouseDoubleClick { button, x, y } => {
                self.input_controller.move_mouse(*x, *y)?;
                self.input_controller.double_click_mouse(button)?;
            }
            AutomationAction::MouseDrag {
                from_x,
                from_y,
                to_x,
                to_y,
            } => {
                self.input_controller
                    .drag_mouse(*from_x, *from_y, *to_x, *to_y)?;
            }
            AutomationAction::MouseScroll { x, y, delta } => {
                self.input_controller.move_mouse(*x, *y)?;
                self.input_controller.scroll_mouse(*delta)?;
            }
            AutomationAction::KeyPress { key } => {
                self.input_controller.press_key(key)?;
            }
            AutomationAction::KeyRelease { key } => {
                self.input_controller.release_key(key)?;
            }
            AutomationAction::KeyCombo { keys } => {
                self.input_controller.key_combo(keys)?;
            }
            AutomationAction::TypeText { text } => {
                self.input_controller.type_text(text)?;
            }
            AutomationAction::WindowFocus { title } => {
                self.window_manager.focus_window(title)?;
            }
            AutomationAction::WindowMove { title, x, y } => {
                self.window_manager.move_window(title, *x, *y)?;
            }
            AutomationAction::WindowResize {
                title,
                width,
                height,
            } => {
                self.window_manager.resize_window(title, *width, *height)?;
            }
            AutomationAction::WindowMaximize { title } => {
                self.window_manager.maximize_window(title)?;
            }
            AutomationAction::WindowMinimize { title } => {
                self.window_manager.minimize_window(title)?;
            }
            AutomationAction::WindowClose { title } => {
                self.window_manager.close_window(title)?;
            }
            AutomationAction::Wait { duration } => {
                std::thread::sleep(*duration);
            }
            AutomationAction::Screenshot { path } => {
                self.take_screenshot(path)?;
            }
            AutomationAction::If { condition, actions } => {
                if self.evaluate_condition(condition)? {
                    log::info!("条件 '{}' 为真，执行动作", condition);
                    for action in actions {
                        self.execute_action(action)?;
                    }
                } else {
                    log::info!("条件 '{}' 为假，跳过动作", condition);
                }
            }
            AutomationAction::Loop { count, actions } => {
                for _ in 0..*count {
                    for action in actions {
                        self.execute_action(action)?;
                    }
                }
            }
            AutomationAction::While { condition, actions } => {
                let mut iteration_count = 0;
                const MAX_ITERATIONS: u32 = 1000; // 防止无限循环

                while self.evaluate_condition(condition)? && iteration_count < MAX_ITERATIONS {
                    log::debug!("While循环第 {} 次迭代: {}", iteration_count + 1, condition);
                    for action in actions {
                        self.execute_action(action)?;
                    }
                    iteration_count += 1;
                }

                if iteration_count >= MAX_ITERATIONS {
                    log::warn!("While循环达到最大迭代次数 {} 次，强制退出", MAX_ITERATIONS);
                }
            }
        }
        Ok(())
    }

    pub fn execute_script(&mut self, actions: &[AutomationAction]) -> Result<()> {
        log::info!("开始执行脚本，共 {} 个动作", actions.len());
        for (i, action) in actions.iter().enumerate() {
            log::debug!("执行动作 {}: {:?}", i + 1, action);
            self.execute_action(action)?;
        }
        log::info!("脚本执行完成");
        Ok(())
    }

    pub fn is_recording(&self) -> bool {
        self.is_recording
    }

    pub fn get_recorded_actions(&self) -> &[AutomationAction] {
        &self.recorded_actions
    }

    fn take_screenshot(&self, path: &str) -> Result<()> {
        use image::{ImageBuffer, RgbaImage};
        use screenshots::Screen;

        let screens = Screen::all();
        if screens.is_empty() {
            return Err(anyhow::anyhow!("未找到可用的屏幕"));
        }

        let screen = &screens[0];
        if let Some(screenshot) = screen.capture() {
            let width = screenshot.width();
            let height = screenshot.height();
            let rgba_data = screenshot.buffer();

            let img: RgbaImage = ImageBuffer::from_raw(width, height, rgba_data.to_vec())
                .ok_or_else(|| anyhow::anyhow!("无法创建图像缓冲区"))?;

            img.save(path)
                .map_err(|e| anyhow::anyhow!("保存截图失败: {}", e))?;
            log::info!("截图已保存到: {}", path);
        } else {
            return Err(anyhow::anyhow!("截图失败"));
        }

        Ok(())
    }

    fn evaluate_condition(&self, condition: &str) -> Result<bool> {
        // 简单的条件表达式解析器
        let condition = condition.trim();

        // 支持的条件格式：
        // "true" / "false" - 布尔值
        // "window_exists:窗口标题" - 检查窗口是否存在
        // "file_exists:文件路径" - 检查文件是否存在
        // "time_after:HH:MM" - 检查当前时间是否在指定时间之后
        // "time_before:HH:MM" - 检查当前时间是否在指定时间之前

        if condition == "true" {
            return Ok(true);
        }

        if condition == "false" {
            return Ok(false);
        }

        if let Some(window_title) = condition.strip_prefix("window_exists:") {
            return Ok(self
                .window_manager
                .find_window_by_title(window_title)?
                .is_some());
        }

        if let Some(file_path) = condition.strip_prefix("file_exists:") {
            return Ok(std::path::Path::new(file_path).exists());
        }

        if let Some(time_str) = condition.strip_prefix("time_after:") {
            return self.check_time_condition(time_str, true);
        }

        if let Some(time_str) = condition.strip_prefix("time_before:") {
            return self.check_time_condition(time_str, false);
        }

        // 默认情况下，非空字符串为真
        Ok(!condition.is_empty())
    }

    fn check_time_condition(&self, time_str: &str, is_after: bool) -> Result<bool> {
        use chrono::{Local, NaiveTime};

        let target_time = NaiveTime::parse_from_str(time_str, "%H:%M")
            .map_err(|e| anyhow::anyhow!("时间格式错误 '{}': {}", time_str, e))?;

        let current_time = Local::now().time();

        if is_after {
            Ok(current_time >= target_time)
        } else {
            Ok(current_time <= target_time)
        }
    }
}
