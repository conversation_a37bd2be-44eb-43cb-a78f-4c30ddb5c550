pub mod input;
pub mod window;

use anyhow::Result;
use lazy_static::lazy_static;
use rdev::{Button, Event, EventType, Key, listen};
use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::{Duration, Instant};

pub use input::InputController;
pub use window::WindowManager;

// 全局录制状态
lazy_static! {
    static ref GLOBAL_RECORDING_STATE: Arc<Mutex<bool>> = Arc::new(Mutex::new(false));
    static ref GLOBAL_RECORDED_ACTIONS: Arc<Mutex<Vec<AutomationAction>>> =
        Arc::new(Mutex::new(Vec::new()));
    static ref GLOBAL_LISTENER_RUNNING: Arc<Mutex<bool>> = Arc::new(Mutex::new(false));
    static ref GLOBAL_LAST_MOUSE_POS: Arc<Mutex<(i32, i32)>> = Arc::new(Mutex::new((0, 0)));
    static ref GLOBAL_CURRENT_MOUSE_POS: Arc<Mutex<(i32, i32)>> = Arc::new(Mutex::new((0, 0)));
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AutomationAction {
    // 鼠标操作
    MouseMove {
        x: i32,
        y: i32,
    },
    MouseClick {
        button: MouseButton,
        x: i32,
        y: i32,
    },
    MouseDoubleClick {
        button: MouseButton,
        x: i32,
        y: i32,
    },
    MouseDrag {
        from_x: i32,
        from_y: i32,
        to_x: i32,
        to_y: i32,
    },
    MouseScroll {
        x: i32,
        y: i32,
        delta: i32,
    },

    // 键盘操作
    KeyPress {
        key: String,
    },
    KeyRelease {
        key: String,
    },
    KeyCombo {
        keys: Vec<String>,
    },
    TypeText {
        text: String,
    },

    // 窗口操作
    WindowFocus {
        title: String,
    },
    WindowMove {
        title: String,
        x: i32,
        y: i32,
    },
    WindowResize {
        title: String,
        width: i32,
        height: i32,
    },
    WindowMaximize {
        title: String,
    },
    WindowMinimize {
        title: String,
    },
    WindowClose {
        title: String,
    },

    // 控制操作
    Wait {
        duration: Duration,
    },
    Screenshot {
        path: String,
    },

    // 条件和循环
    If {
        condition: String,
        actions: Vec<AutomationAction>,
    },
    Loop {
        count: u32,
        actions: Vec<AutomationAction>,
    },
    While {
        condition: String,
        actions: Vec<AutomationAction>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MouseButton {
    Left,
    Right,
    Middle,
}

pub struct AutomationEngine {
    input_controller: InputController,
    window_manager: WindowManager,
    recording_thread: Option<thread::JoinHandle<()>>,
}

impl Default for AutomationEngine {
    fn default() -> Self {
        Self::new()
    }
}

impl AutomationEngine {
    pub fn new() -> Self {
        Self {
            input_controller: InputController::new(),
            window_manager: WindowManager::new(),
            recording_thread: None,
        }
    }

    pub fn start_recording(&mut self) -> Result<()> {
        // 检查权限（仅在macOS上）
        #[cfg(target_os = "macos")]
        {
            self.check_macos_permissions();
        }

        // 检查是否已经有监听器在运行
        if *GLOBAL_LISTENER_RUNNING.lock().unwrap() {
            log::warn!("录制监听器已经在运行中，尝试重置状态");
            // 强制重置状态，允许重新开始录制
            *GLOBAL_RECORDING_STATE.lock().unwrap() = false;
            *GLOBAL_LISTENER_RUNNING.lock().unwrap() = false;

            // 如果有旧的线程句柄，清理它
            if let Some(handle) = self.recording_thread.take() {
                // 不等待旧线程，让它自然结束
                std::mem::drop(handle);
                log::debug!("清理旧的录制线程句柄");
            }

            // 等待一小段时间确保状态重置
            thread::sleep(Duration::from_millis(200));
        }

        // 清空之前的录制数据
        GLOBAL_RECORDED_ACTIONS.lock().unwrap().clear();

        // 设置录制状态
        *GLOBAL_RECORDING_STATE.lock().unwrap() = true;
        *GLOBAL_LISTENER_RUNNING.lock().unwrap() = true;

        // 启动录制线程，但不等待它
        let handle = thread::spawn(move || {
            Self::recording_loop();
        });

        self.recording_thread = Some(handle);
        log::info!("开始录制自动化脚本");
        Ok(())
    }

    pub fn stop_recording(&mut self) -> Result<Vec<AutomationAction>> {
        // 停止录制
        *GLOBAL_RECORDING_STATE.lock().unwrap() = false;
        log::info!("已发送停止录制信号");

        // 等待一小段时间让录制线程有机会处理停止信号
        thread::sleep(Duration::from_millis(100));

        // 获取录制的动作
        let actions = GLOBAL_RECORDED_ACTIONS.lock().unwrap().clone();
        log::info!("停止录制，共录制了 {} 个动作", actions.len());

        // 重置监听器状态，确保下次可以正常开始录制
        *GLOBAL_LISTENER_RUNNING.lock().unwrap() = false;
        log::debug!("重置监听器状态");

        // 清理线程句柄（但不等待太久）
        if let Some(handle) = self.recording_thread.take() {
            // 尝试等待线程结束，但设置超时
            let _handle_clone = thread::spawn(move || {
                let _ = handle.join();
            });

            // 等待最多500毫秒
            thread::sleep(Duration::from_millis(500));
            log::debug!("录制线程清理完成");
        }

        Ok(actions)
    }

    pub fn execute_action(&mut self, action: &AutomationAction) -> Result<()> {
        match action {
            AutomationAction::MouseMove { x, y } => {
                self.input_controller.move_mouse(*x, *y)?;
            }
            AutomationAction::MouseClick { button, x, y } => {
                self.input_controller.move_mouse(*x, *y)?;
                self.input_controller.click_mouse(button)?;
            }
            AutomationAction::MouseDoubleClick { button, x, y } => {
                self.input_controller.move_mouse(*x, *y)?;
                self.input_controller.double_click_mouse(button)?;
            }
            AutomationAction::MouseDrag {
                from_x,
                from_y,
                to_x,
                to_y,
            } => {
                self.input_controller
                    .drag_mouse(*from_x, *from_y, *to_x, *to_y)?;
            }
            AutomationAction::MouseScroll { x, y, delta } => {
                self.input_controller.move_mouse(*x, *y)?;
                self.input_controller.scroll_mouse(*delta)?;
            }
            AutomationAction::KeyPress { key } => {
                self.input_controller.press_key(key)?;
            }
            AutomationAction::KeyRelease { key } => {
                self.input_controller.release_key(key)?;
            }
            AutomationAction::KeyCombo { keys } => {
                self.input_controller.key_combo(keys)?;
            }
            AutomationAction::TypeText { text } => {
                self.input_controller.type_text(text)?;
            }
            AutomationAction::WindowFocus { title } => {
                self.window_manager.focus_window(title)?;
            }
            AutomationAction::WindowMove { title, x, y } => {
                self.window_manager.move_window(title, *x, *y)?;
            }
            AutomationAction::WindowResize {
                title,
                width,
                height,
            } => {
                self.window_manager.resize_window(title, *width, *height)?;
            }
            AutomationAction::WindowMaximize { title } => {
                self.window_manager.maximize_window(title)?;
            }
            AutomationAction::WindowMinimize { title } => {
                self.window_manager.minimize_window(title)?;
            }
            AutomationAction::WindowClose { title } => {
                self.window_manager.close_window(title)?;
            }
            AutomationAction::Wait { duration } => {
                std::thread::sleep(*duration);
            }
            AutomationAction::Screenshot { path } => {
                self.take_screenshot(path)?;
            }
            AutomationAction::If { condition, actions } => {
                if self.evaluate_condition(condition)? {
                    log::info!("条件 '{}' 为真，执行动作", condition);
                    for action in actions {
                        self.execute_action(action)?;
                    }
                } else {
                    log::info!("条件 '{}' 为假，跳过动作", condition);
                }
            }
            AutomationAction::Loop { count, actions } => {
                for _ in 0..*count {
                    for action in actions {
                        self.execute_action(action)?;
                    }
                }
            }
            AutomationAction::While { condition, actions } => {
                let mut iteration_count = 0;
                const MAX_ITERATIONS: u32 = 1000; // 防止无限循环

                while self.evaluate_condition(condition)? && iteration_count < MAX_ITERATIONS {
                    log::debug!("While循环第 {} 次迭代: {}", iteration_count + 1, condition);
                    for action in actions {
                        self.execute_action(action)?;
                    }
                    iteration_count += 1;
                }

                if iteration_count >= MAX_ITERATIONS {
                    log::warn!("While循环达到最大迭代次数 {} 次，强制退出", MAX_ITERATIONS);
                }
            }
        }
        Ok(())
    }

    pub fn execute_script(&mut self, actions: &[AutomationAction]) -> Result<()> {
        log::info!("开始执行脚本，共 {} 个动作", actions.len());
        for (i, action) in actions.iter().enumerate() {
            log::debug!("执行动作 {}: {:?}", i + 1, action);
            self.execute_action(action)?;
        }
        log::info!("脚本执行完成");
        Ok(())
    }

    pub fn is_recording(&self) -> bool {
        *GLOBAL_RECORDING_STATE.lock().unwrap()
    }

    pub fn get_recorded_actions(&self) -> Vec<AutomationAction> {
        GLOBAL_RECORDED_ACTIONS.lock().unwrap().clone()
    }

    fn take_screenshot(&self, path: &str) -> Result<()> {
        use image::{ImageBuffer, RgbaImage};
        use screenshots::Screen;

        let screens = Screen::all();
        if screens.is_empty() {
            return Err(anyhow::anyhow!("未找到可用的屏幕"));
        }

        let screen = &screens[0];
        if let Some(screenshot) = screen.capture() {
            let width = screenshot.width();
            let height = screenshot.height();
            let rgba_data = screenshot.buffer();

            let img: RgbaImage = ImageBuffer::from_raw(width, height, rgba_data.to_vec())
                .ok_or_else(|| anyhow::anyhow!("无法创建图像缓冲区"))?;

            img.save(path)
                .map_err(|e| anyhow::anyhow!("保存截图失败: {}", e))?;
            log::info!("截图已保存到: {}", path);
        } else {
            return Err(anyhow::anyhow!("截图失败"));
        }

        Ok(())
    }

    fn evaluate_condition(&self, condition: &str) -> Result<bool> {
        // 简单的条件表达式解析器
        let condition = condition.trim();

        // 支持的条件格式：
        // "true" / "false" - 布尔值
        // "window_exists:窗口标题" - 检查窗口是否存在
        // "file_exists:文件路径" - 检查文件是否存在
        // "time_after:HH:MM" - 检查当前时间是否在指定时间之后
        // "time_before:HH:MM" - 检查当前时间是否在指定时间之前

        if condition == "true" {
            return Ok(true);
        }

        if condition == "false" {
            return Ok(false);
        }

        if let Some(window_title) = condition.strip_prefix("window_exists:") {
            return Ok(self
                .window_manager
                .find_window_by_title(window_title)?
                .is_some());
        }

        if let Some(file_path) = condition.strip_prefix("file_exists:") {
            return Ok(std::path::Path::new(file_path).exists());
        }

        if let Some(time_str) = condition.strip_prefix("time_after:") {
            return self.check_time_condition(time_str, true);
        }

        if let Some(time_str) = condition.strip_prefix("time_before:") {
            return self.check_time_condition(time_str, false);
        }

        // 默认情况下，非空字符串为真
        Ok(!condition.is_empty())
    }

    fn check_time_condition(&self, time_str: &str, is_after: bool) -> Result<bool> {
        use chrono::{Local, NaiveTime};

        let target_time = NaiveTime::parse_from_str(time_str, "%H:%M")
            .map_err(|e| anyhow::anyhow!("时间格式错误 '{}': {}", time_str, e))?;

        let current_time = Local::now().time();

        if is_after {
            Ok(current_time >= target_time)
        } else {
            Ok(current_time <= target_time)
        }
    }

    fn recording_loop() {
        log::debug!("录制监听器线程启动");

        // 开始监听事件
        if let Err(error) = listen(Self::event_callback) {
            log::error!("事件监听错误: {:?}", error);
        }

        // 监听结束后，重置监听器状态
        *GLOBAL_LISTENER_RUNNING.lock().unwrap() = false;
        log::debug!("录制监听器线程结束");
    }

    fn event_callback(event: Event) {
        // 检查是否还在录制
        let is_recording = {
            let recording_state = GLOBAL_RECORDING_STATE.lock().unwrap();
            *recording_state
        };

        if !is_recording {
            // 如果不在录制状态，直接返回，这样可以让listen函数更快地退出
            return;
        }

        // 添加调试信息来跟踪所有事件
        log::debug!("收到事件: {:?}", event.event_type);

        let action = match event.event_type {
            EventType::MouseMove { x, y } => {
                let current_x = x as i32;
                let current_y = y as i32;

                // 只更新当前鼠标位置，不记录移动事件
                *GLOBAL_CURRENT_MOUSE_POS.lock().unwrap() = (current_x, current_y);

                None // 不记录鼠标移动
            }
            EventType::ButtonPress(button) => {
                let mouse_button = match button {
                    Button::Left => MouseButton::Left,
                    Button::Right => MouseButton::Right,
                    Button::Middle => MouseButton::Middle,
                    _ => return, // 忽略其他按钮
                };

                // 获取当前鼠标位置
                let current_pos = *GLOBAL_CURRENT_MOUSE_POS.lock().unwrap();

                Some(AutomationAction::MouseClick {
                    button: mouse_button,
                    x: current_pos.0,
                    y: current_pos.1,
                })
            }
            EventType::KeyPress(key) => {
                let key_str = Self::key_to_string(key);
                Some(AutomationAction::KeyPress { key: key_str })
            }
            EventType::KeyRelease(key) => {
                let key_str = Self::key_to_string(key);
                Some(AutomationAction::KeyRelease { key: key_str })
            }
            EventType::Wheel {
                delta_x: _,
                delta_y,
            } => {
                // 获取当前鼠标位置
                let current_pos = *GLOBAL_CURRENT_MOUSE_POS.lock().unwrap();

                Some(AutomationAction::MouseScroll {
                    x: current_pos.0,
                    y: current_pos.1,
                    delta: delta_y as i32,
                })
            }
            _ => None,
        };

        if let Some(action) = action {
            if let Ok(mut actions) = GLOBAL_RECORDED_ACTIONS.lock() {
                actions.push(action);
                log::debug!("录制动作: {:?}", actions.last().unwrap());
            }
        }
    }

    fn key_to_string(key: Key) -> String {
        match key {
            Key::Alt => "alt".to_string(),
            Key::AltGr => "altgr".to_string(),
            Key::Backspace => "backspace".to_string(),
            Key::CapsLock => "capslock".to_string(),
            Key::ControlLeft => "ctrl".to_string(),
            Key::ControlRight => "ctrl".to_string(),
            Key::Delete => "delete".to_string(),
            Key::DownArrow => "down".to_string(),
            Key::End => "end".to_string(),
            Key::Escape => "escape".to_string(),
            Key::F1 => "f1".to_string(),
            Key::F2 => "f2".to_string(),
            Key::F3 => "f3".to_string(),
            Key::F4 => "f4".to_string(),
            Key::F5 => "f5".to_string(),
            Key::F6 => "f6".to_string(),
            Key::F7 => "f7".to_string(),
            Key::F8 => "f8".to_string(),
            Key::F9 => "f9".to_string(),
            Key::F10 => "f10".to_string(),
            Key::F11 => "f11".to_string(),
            Key::F12 => "f12".to_string(),
            Key::Home => "home".to_string(),
            Key::LeftArrow => "left".to_string(),
            Key::MetaLeft => "meta".to_string(),
            Key::MetaRight => "meta".to_string(),
            Key::PageDown => "pagedown".to_string(),
            Key::PageUp => "pageup".to_string(),
            Key::Return => "return".to_string(),
            Key::RightArrow => "right".to_string(),
            Key::ShiftLeft => "shift".to_string(),
            Key::ShiftRight => "shift".to_string(),
            Key::Space => "space".to_string(),
            Key::Tab => "tab".to_string(),
            Key::UpArrow => "up".to_string(),
            Key::PrintScreen => "printscreen".to_string(),
            Key::ScrollLock => "scrolllock".to_string(),
            Key::Pause => "pause".to_string(),
            Key::NumLock => "numlock".to_string(),
            Key::BackQuote => "`".to_string(),
            Key::Num1 => "1".to_string(),
            Key::Num2 => "2".to_string(),
            Key::Num3 => "3".to_string(),
            Key::Num4 => "4".to_string(),
            Key::Num5 => "5".to_string(),
            Key::Num6 => "6".to_string(),
            Key::Num7 => "7".to_string(),
            Key::Num8 => "8".to_string(),
            Key::Num9 => "9".to_string(),
            Key::Num0 => "0".to_string(),
            Key::Minus => "-".to_string(),
            Key::Equal => "=".to_string(),
            Key::KeyQ => "q".to_string(),
            Key::KeyW => "w".to_string(),
            Key::KeyE => "e".to_string(),
            Key::KeyR => "r".to_string(),
            Key::KeyT => "t".to_string(),
            Key::KeyY => "y".to_string(),
            Key::KeyU => "u".to_string(),
            Key::KeyI => "i".to_string(),
            Key::KeyO => "o".to_string(),
            Key::KeyP => "p".to_string(),
            Key::LeftBracket => "[".to_string(),
            Key::RightBracket => "]".to_string(),
            Key::KeyA => "a".to_string(),
            Key::KeyS => "s".to_string(),
            Key::KeyD => "d".to_string(),
            Key::KeyF => "f".to_string(),
            Key::KeyG => "g".to_string(),
            Key::KeyH => "h".to_string(),
            Key::KeyJ => "j".to_string(),
            Key::KeyK => "k".to_string(),
            Key::KeyL => "l".to_string(),
            Key::SemiColon => ";".to_string(),
            Key::Quote => "'".to_string(),
            Key::BackSlash => "\\".to_string(),
            Key::IntlBackslash => "\\".to_string(),
            Key::KeyZ => "z".to_string(),
            Key::KeyX => "x".to_string(),
            Key::KeyC => "c".to_string(),
            Key::KeyV => "v".to_string(),
            Key::KeyB => "b".to_string(),
            Key::KeyN => "n".to_string(),
            Key::KeyM => "m".to_string(),
            Key::Comma => ",".to_string(),
            Key::Dot => ".".to_string(),
            Key::Slash => "/".to_string(),
            Key::Insert => "insert".to_string(),
            Key::KpReturn => "return".to_string(),
            Key::KpMinus => "-".to_string(),
            Key::KpPlus => "+".to_string(),
            Key::KpMultiply => "*".to_string(),
            Key::KpDivide => "/".to_string(),
            Key::Kp0 => "0".to_string(),
            Key::Kp1 => "1".to_string(),
            Key::Kp2 => "2".to_string(),
            Key::Kp3 => "3".to_string(),
            Key::Kp4 => "4".to_string(),
            Key::Kp5 => "5".to_string(),
            Key::Kp6 => "6".to_string(),
            Key::Kp7 => "7".to_string(),
            Key::Kp8 => "8".to_string(),
            Key::Kp9 => "9".to_string(),
            Key::KpDelete => "delete".to_string(),
            Key::Function => "function".to_string(),
            _ => format!("{:?}", key).to_lowercase(),
        }
    }

    #[cfg(target_os = "macos")]
    fn check_macos_permissions(&self) {
        log::info!("检查macOS权限...");

        // 检查辅助功能权限
        let has_accessibility = self.check_accessibility_permission();
        if !has_accessibility {
            log::warn!("⚠️  缺少辅助功能权限！");
            log::warn!("请在 系统偏好设置 → 安全性与隐私 → 隐私 → 辅助功能 中添加此应用程序");
            log::warn!(
                "应用程序路径: {:?}",
                std::env::current_exe().unwrap_or_default()
            );
            log::warn!("没有此权限，键盘事件可能无法录制！");
        } else {
            log::info!("✅ 辅助功能权限已授予");
        }
    }

    #[cfg(target_os = "macos")]
    fn check_accessibility_permission(&self) -> bool {
        // 在macOS上检查辅助功能权限
        // 这是一个简化的检查，实际的权限检查需要使用系统API
        use std::process::Command;

        // 尝试使用系统命令检查权限
        let output = Command::new("osascript")
            .arg("-e")
            .arg("tell application \"System Events\" to get name of every process")
            .output();

        match output {
            Ok(result) => {
                if result.status.success() {
                    log::debug!("辅助功能权限检查通过");
                    true
                } else {
                    log::debug!(
                        "辅助功能权限检查失败: {:?}",
                        String::from_utf8_lossy(&result.stderr)
                    );
                    false
                }
            }
            Err(e) => {
                log::debug!("无法执行权限检查: {}", e);
                false
            }
        }
    }
}
