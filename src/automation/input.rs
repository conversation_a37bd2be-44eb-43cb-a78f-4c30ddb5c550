use crate::automation::Mouse<PERSON>utton;
use anyhow::Result;

pub struct InputController {
    #[cfg(windows)]
    _windows_handle: (),
    #[cfg(all(unix, not(target_os = "macos")))]
    _x11_display: Option<*mut x11::xlib::Display>,
    #[cfg(target_os = "macos")]
    _macos_handle: (),
}

impl InputController {
    pub fn new() -> Self {
        Self {
            #[cfg(windows)]
            _windows_handle: (),
            #[cfg(all(unix, not(target_os = "macos")))]
            _x11_display: None,
            #[cfg(target_os = "macos")]
            _macos_handle: (),
        }
    }

    pub fn move_mouse(&self, x: i32, y: i32) -> Result<()> {
        #[cfg(windows)]
        {
            use windows::Win32::UI::Input::KeyboardAndMouse::SetCursorPos;
            unsafe {
                SetCursorPos(x, y)?;
            }
        }

        #[cfg(target_os = "macos")]
        {
            self.move_mouse_macos(x, y)?;
        }

        #[cfg(all(unix, not(target_os = "macos")))]
        {
            // TODO: 实现Linux下的鼠标移动
            log::info!("移动鼠标到 ({}, {})", x, y);
        }

        Ok(())
    }

    pub fn click_mouse(&self, button: &MouseButton) -> Result<()> {
        #[cfg(windows)]
        {
            use windows::Win32::UI::Input::KeyboardAndMouse::{
                MOUSEEVENTF_LEFTDOWN, MOUSEEVENTF_LEFTUP, MOUSEEVENTF_MIDDLEDOWN,
                MOUSEEVENTF_MIDDLEUP, MOUSEEVENTF_RIGHTDOWN, MOUSEEVENTF_RIGHTUP, mouse_event,
            };

            let (down_flag, up_flag) = match button {
                MouseButton::Left => (MOUSEEVENTF_LEFTDOWN, MOUSEEVENTF_LEFTUP),
                MouseButton::Right => (MOUSEEVENTF_RIGHTDOWN, MOUSEEVENTF_RIGHTUP),
                MouseButton::Middle => (MOUSEEVENTF_MIDDLEDOWN, MOUSEEVENTF_MIDDLEUP),
            };

            unsafe {
                mouse_event(down_flag, 0, 0, 0, 0);
                std::thread::sleep(std::time::Duration::from_millis(50));
                mouse_event(up_flag, 0, 0, 0, 0);
            }
        }

        #[cfg(target_os = "macos")]
        {
            self.click_mouse_macos(button)?;
        }

        #[cfg(all(unix, not(target_os = "macos")))]
        {
            // TODO: 实现Linux下的鼠标点击
            log::info!("点击鼠标按钮: {:?}", button);
        }

        Ok(())
    }

    pub fn double_click_mouse(&self, button: &MouseButton) -> Result<()> {
        self.click_mouse(button)?;
        std::thread::sleep(std::time::Duration::from_millis(100));
        self.click_mouse(button)?;
        Ok(())
    }

    pub fn drag_mouse(&self, from_x: i32, from_y: i32, to_x: i32, to_y: i32) -> Result<()> {
        self.move_mouse(from_x, from_y)?;

        #[cfg(windows)]
        {
            use windows::Win32::UI::Input::KeyboardAndMouse::{
                MOUSEEVENTF_LEFTDOWN, MOUSEEVENTF_LEFTUP, mouse_event,
            };

            unsafe {
                mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
                std::thread::sleep(std::time::Duration::from_millis(100));
            }
        }

        self.move_mouse(to_x, to_y)?;

        #[cfg(windows)]
        {
            use windows::Win32::UI::Input::KeyboardAndMouse::{MOUSEEVENTF_LEFTUP, mouse_event};

            unsafe {
                mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
            }
        }

        #[cfg(unix)]
        {
            // TODO: 实现Linux下的鼠标拖拽
            log::info!(
                "拖拽鼠标从 ({}, {}) 到 ({}, {})",
                from_x,
                from_y,
                to_x,
                to_y
            );
        }

        Ok(())
    }

    pub fn scroll_mouse(&self, delta: i32) -> Result<()> {
        #[cfg(windows)]
        {
            use windows::Win32::UI::Input::KeyboardAndMouse::{MOUSEEVENTF_WHEEL, mouse_event};

            unsafe {
                mouse_event(MOUSEEVENTF_WHEEL, 0, 0, (delta * 120) as u32, 0);
            }
        }

        #[cfg(unix)]
        {
            // TODO: 实现Linux下的鼠标滚轮
            log::info!("滚动鼠标滚轮: {}", delta);
        }

        Ok(())
    }

    pub fn press_key(&self, key: &str) -> Result<()> {
        #[cfg(windows)]
        {
            use windows::Win32::UI::Input::KeyboardAndMouse::{
                KEYEVENTF_KEYUP, VK_BACK, VK_DELETE, VK_ESCAPE, VK_RETURN, VK_SPACE, VK_TAB,
                keybd_event,
            };

            let vk_code = self.get_virtual_key_code(key)?;
            unsafe {
                keybd_event(vk_code as u8, 0, 0, 0);
            }
            log::debug!("按下按键: {}", key);
        }

        #[cfg(target_os = "macos")]
        {
            // macOS下使用Core Graphics
            self.press_key_macos(key)?;
        }

        #[cfg(all(unix, not(target_os = "macos")))]
        {
            // Linux下使用X11
            self.press_key_x11(key)?;
        }

        Ok(())
    }

    pub fn release_key(&self, key: &str) -> Result<()> {
        #[cfg(windows)]
        {
            use windows::Win32::UI::Input::KeyboardAndMouse::{KEYEVENTF_KEYUP, keybd_event};

            let vk_code = self.get_virtual_key_code(key)?;
            unsafe {
                keybd_event(vk_code as u8, 0, KEYEVENTF_KEYUP, 0);
            }
            log::debug!("释放按键: {}", key);
        }

        #[cfg(target_os = "macos")]
        {
            self.release_key_macos(key)?;
        }

        #[cfg(all(unix, not(target_os = "macos")))]
        {
            self.release_key_x11(key)?;
        }

        Ok(())
    }

    pub fn key_combo(&self, keys: &[String]) -> Result<()> {
        // 按下所有按键
        for key in keys {
            self.press_key(key)?;
            std::thread::sleep(std::time::Duration::from_millis(10));
        }

        // 释放所有按键（逆序）
        for key in keys.iter().rev() {
            self.release_key(key)?;
            std::thread::sleep(std::time::Duration::from_millis(10));
        }

        Ok(())
    }

    pub fn type_text(&self, text: &str) -> Result<()> {
        #[cfg(windows)]
        {
            self.type_text_windows(text)?;
        }

        #[cfg(target_os = "macos")]
        {
            self.type_text_macos(text)?;
        }

        #[cfg(all(unix, not(target_os = "macos")))]
        {
            self.type_text_x11(text)?;
        }

        Ok(())
    }

    #[cfg(windows)]
    fn get_virtual_key_code(&self, key: &str) -> Result<u16> {
        use windows::Win32::UI::Input::KeyboardAndMouse::*;

        let vk_code = match key.to_lowercase().as_str() {
            "enter" | "return" => VK_RETURN.0,
            "space" => VK_SPACE.0,
            "escape" | "esc" => VK_ESCAPE.0,
            "tab" => VK_TAB.0,
            "backspace" => VK_BACK.0,
            "delete" => VK_DELETE.0,
            "shift" => VK_SHIFT.0,
            "ctrl" | "control" => VK_CONTROL.0,
            "alt" => VK_MENU.0,
            "win" | "windows" => VK_LWIN.0,
            "f1" => VK_F1.0,
            "f2" => VK_F2.0,
            "f3" => VK_F3.0,
            "f4" => VK_F4.0,
            "f5" => VK_F5.0,
            "f6" => VK_F6.0,
            "f7" => VK_F7.0,
            "f8" => VK_F8.0,
            "f9" => VK_F9.0,
            "f10" => VK_F10.0,
            "f11" => VK_F11.0,
            "f12" => VK_F12.0,
            "up" => VK_UP.0,
            "down" => VK_DOWN.0,
            "left" => VK_LEFT.0,
            "right" => VK_RIGHT.0,
            "home" => VK_HOME.0,
            "end" => VK_END.0,
            "pageup" => VK_PRIOR.0,
            "pagedown" => VK_NEXT.0,
            "insert" => VK_INSERT.0,
            _ => {
                // 对于单个字符，使用VkKeyScan
                if key.len() == 1 {
                    let ch = key.chars().next().unwrap();
                    if ch.is_ascii() {
                        use windows::Win32::UI::Input::KeyboardAndMouse::VkKeyScanA;
                        unsafe {
                            let result = VkKeyScanA(ch as u8);
                            if result != -1 {
                                (result & 0xFF) as u16
                            } else {
                                return Err(anyhow::anyhow!("无法获取按键 '{}' 的虚拟键码", key));
                            }
                        }
                    } else {
                        return Err(anyhow::anyhow!("不支持的字符: {}", key));
                    }
                } else {
                    return Err(anyhow::anyhow!("不支持的按键: {}", key));
                }
            }
        };

        Ok(vk_code)
    }

    #[cfg(target_os = "macos")]
    fn press_key_macos(&self, key: &str) -> Result<()> {
        use core_graphics::event::CGEvent;
        use core_graphics::event_source::{CGEventSource, CGEventSourceStateID};

        let key_code = self.get_macos_key_code(key)?;
        let source = CGEventSource::new(CGEventSourceStateID::HIDSystemState)
            .map_err(|_| anyhow::anyhow!("无法创建事件源"))?;

        let event = CGEvent::new_keyboard_event(source, key_code, true)
            .map_err(|_| anyhow::anyhow!("无法创建键盘事件"))?;

        event.post(core_graphics::event::CGEventTapLocation::HID);
        log::debug!("macOS按下按键: {}", key);
        Ok(())
    }

    #[cfg(target_os = "macos")]
    fn get_macos_key_code(&self, key: &str) -> Result<core_graphics::event::CGKeyCode> {
        let key_code = match key.to_lowercase().as_str() {
            "a" => 0x00,
            "s" => 0x01,
            "d" => 0x02,
            "f" => 0x03,
            "h" => 0x04,
            "g" => 0x05,
            "z" => 0x06,
            "x" => 0x07,
            "c" => 0x08,
            "v" => 0x09,
            "b" => 0x0B,
            "q" => 0x0C,
            "w" => 0x0D,
            "e" => 0x0E,
            "r" => 0x0F,
            "y" => 0x10,
            "t" => 0x11,
            "1" => 0x12,
            "2" => 0x13,
            "3" => 0x14,
            "4" => 0x15,
            "6" => 0x16,
            "5" => 0x17,
            "=" => 0x18,
            "9" => 0x19,
            "7" => 0x1A,
            "-" => 0x1B,
            "8" => 0x1C,
            "0" => 0x1D,
            "]" => 0x1E,
            "o" => 0x1F,
            "u" => 0x20,
            "[" => 0x21,
            "i" => 0x22,
            "p" => 0x23,
            "return" | "enter" => 0x24,
            "l" => 0x25,
            "j" => 0x26,
            "'" => 0x27,
            "k" => 0x28,
            ";" => 0x29,
            "\\" => 0x2A,
            "," => 0x2B,
            "/" => 0x2C,
            "n" => 0x2D,
            "m" => 0x2E,
            "." => 0x2F,
            "tab" => 0x30,
            "space" => 0x31,
            "`" => 0x32,
            "delete" | "backspace" => 0x33,
            "escape" | "esc" => 0x35,
            "cmd" | "command" => 0x37,
            "shift" => 0x38,
            "capslock" => 0x39,
            "option" | "alt" => 0x3A,
            "ctrl" | "control" => 0x3B,
            "f1" => 0x7A,
            "f2" => 0x78,
            "f3" => 0x63,
            "f4" => 0x76,
            "f5" => 0x60,
            "f6" => 0x61,
            "f7" => 0x62,
            "f8" => 0x64,
            "f9" => 0x65,
            "f10" => 0x6D,
            "f11" => 0x67,
            "f12" => 0x6F,
            "up" => 0x7E,
            "down" => 0x7D,
            "left" => 0x7B,
            "right" => 0x7C,
            _ => return Err(anyhow::anyhow!("不支持的macOS按键: {}", key)),
        };

        Ok(key_code)
    }

    #[cfg(target_os = "macos")]
    fn release_key_macos(&self, key: &str) -> Result<()> {
        use core_graphics::event::CGEvent;
        use core_graphics::event_source::{CGEventSource, CGEventSourceStateID};

        let key_code = self.get_macos_key_code(key)?;
        let source = CGEventSource::new(CGEventSourceStateID::HIDSystemState)
            .map_err(|_| anyhow::anyhow!("无法创建事件源"))?;

        let event = CGEvent::new_keyboard_event(source, key_code, false)
            .map_err(|_| anyhow::anyhow!("无法创建键盘事件"))?;

        event.post(core_graphics::event::CGEventTapLocation::HID);
        log::debug!("macOS释放按键: {}", key);
        Ok(())
    }

    #[cfg(all(unix, not(target_os = "macos")))]
    fn press_key_x11(&self, key: &str) -> Result<()> {
        // TODO: 实现X11按键按下
        log::info!("X11按下按键: {}", key);
        Ok(())
    }

    #[cfg(all(unix, not(target_os = "macos")))]
    fn release_key_x11(&self, key: &str) -> Result<()> {
        // TODO: 实现X11按键释放
        log::info!("X11释放按键: {}", key);
        Ok(())
    }

    #[cfg(windows)]
    fn type_text_windows(&self, text: &str) -> Result<()> {
        use windows::Win32::UI::Input::KeyboardAndMouse::{
            INPUT, INPUT_KEYBOARD, KEYBDINPUT, KEYEVENTF_UNICODE, SendInput,
        };

        for ch in text.chars() {
            let mut input = INPUT {
                r#type: INPUT_KEYBOARD,
                Anonymous: windows::Win32::UI::Input::KeyboardAndMouse::INPUT_0 {
                    ki: KEYBDINPUT {
                        wVk: windows::Win32::UI::Input::KeyboardAndMouse::VIRTUAL_KEY(0),
                        wScan: ch as u16,
                        dwFlags: KEYEVENTF_UNICODE,
                        time: 0,
                        dwExtraInfo: 0,
                    },
                },
            };

            unsafe {
                SendInput(&[input], std::mem::size_of::<INPUT>() as i32);
            }

            // 短暂延迟以确保字符正确输入
            std::thread::sleep(std::time::Duration::from_millis(1));
        }

        log::debug!("Windows输入文本: {}", text);
        Ok(())
    }

    #[cfg(target_os = "macos")]
    fn type_text_macos(&self, text: &str) -> Result<()> {
        // 对于macOS，我们逐个字符输入，使用键盘事件
        for ch in text.chars() {
            // 对于简单的ASCII字符，我们可以尝试映射到键码
            if ch.is_ascii_alphanumeric() || ch.is_ascii_punctuation() || ch == ' ' {
                let key_str = ch.to_string();
                if let Ok(_) = self.get_macos_key_code(&key_str) {
                    self.press_key_macos(&key_str)?;
                    std::thread::sleep(std::time::Duration::from_millis(10));
                    self.release_key_macos(&key_str)?;
                } else {
                    // 对于无法映射的字符，记录警告
                    log::warn!("无法输入字符: {}", ch);
                }
            } else {
                log::warn!("不支持的字符: {}", ch);
            }

            // 短暂延迟
            std::thread::sleep(std::time::Duration::from_millis(10));
        }

        log::debug!("macOS输入文本: {}", text);
        Ok(())
    }

    #[cfg(all(unix, not(target_os = "macos")))]
    fn type_text_x11(&self, text: &str) -> Result<()> {
        // TODO: 实现X11文本输入
        log::info!("X11输入文本: {}", text);
        Ok(())
    }

    #[cfg(target_os = "macos")]
    fn move_mouse_macos(&self, x: i32, y: i32) -> Result<()> {
        use core_graphics::display::CGDisplay;
        use core_graphics::display::CGDisplayMoveCursorToPoint;
        use core_graphics::geometry::CGPoint;

        // 方法1: 使用CGDisplayMoveCursorToPoint (更直接的方法)
        let point = CGPoint::new(x as f64, y as f64);
        let main_display = CGDisplay::main();

        let result = unsafe { CGDisplayMoveCursorToPoint(main_display.id, point) };
        if result != 0 {
            log::warn!("CGDisplayMoveCursorToPoint返回错误代码: {}", result);
            // 如果直接移动失败，尝试使用事件方法
            return self.move_mouse_macos_event(x, y);
        }

        log::debug!(
            "macOS移动鼠标到 ({}, {}) - 使用CGDisplayMoveCursorToPoint",
            x,
            y
        );
        Ok(())
    }

    #[cfg(target_os = "macos")]
    fn move_mouse_macos_event(&self, x: i32, y: i32) -> Result<()> {
        use core_graphics::event::{CGEvent, CGEventType};
        use core_graphics::event_source::{CGEventSource, CGEventSourceStateID};
        use core_graphics::geometry::CGPoint;

        let source = CGEventSource::new(CGEventSourceStateID::HIDSystemState)
            .map_err(|_| anyhow::anyhow!("无法创建事件源"))?;

        let point = CGPoint::new(x as f64, y as f64);

        // 尝试不同的事件类型和发送位置
        let event = CGEvent::new_mouse_event(
            source,
            CGEventType::MouseMoved,
            point,
            core_graphics::event::CGMouseButton::Left,
        )
        .map_err(|_| anyhow::anyhow!("无法创建鼠标移动事件"))?;

        // 尝试不同的发送位置
        event.post(core_graphics::event::CGEventTapLocation::HID);

        // 添加小延迟确保事件被处理
        std::thread::sleep(std::time::Duration::from_millis(1));

        log::debug!("macOS移动鼠标到 ({}, {}) - 使用CGEvent", x, y);
        Ok(())
    }

    #[cfg(target_os = "macos")]
    fn click_mouse_macos(&self, button: &MouseButton) -> Result<()> {
        use core_graphics::display::CGDisplay;
        use core_graphics::event::{CGEvent, CGEventType, CGMouseButton};
        use core_graphics::event_source::{CGEventSource, CGEventSourceStateID};
        use core_graphics::geometry::CGPoint;

        let source = CGEventSource::new(CGEventSourceStateID::HIDSystemState)
            .map_err(|_| anyhow::anyhow!("无法创建事件源"))?;

        // 获取当前鼠标位置 - 使用更可靠的方法
        let current_location = unsafe {
            // 使用CGEventCreate获取当前鼠标位置
            let null_event =
                CGEvent::new(source.clone()).map_err(|_| anyhow::anyhow!("无法创建空事件"))?;
            null_event.location()
        };

        log::debug!(
            "当前鼠标位置: ({}, {})",
            current_location.x,
            current_location.y
        );

        let (cg_button, down_event_type, up_event_type) = match button {
            MouseButton::Left => (
                CGMouseButton::Left,
                CGEventType::LeftMouseDown,
                CGEventType::LeftMouseUp,
            ),
            MouseButton::Right => (
                CGMouseButton::Right,
                CGEventType::RightMouseDown,
                CGEventType::RightMouseUp,
            ),
            MouseButton::Middle => (
                CGMouseButton::Center,
                CGEventType::OtherMouseDown,
                CGEventType::OtherMouseUp,
            ),
        };

        // 创建鼠标按下事件
        let down_event =
            CGEvent::new_mouse_event(source.clone(), down_event_type, current_location, cg_button)
                .map_err(|_| anyhow::anyhow!("无法创建鼠标按下事件"))?;

        // 创建鼠标释放事件
        let up_event = CGEvent::new_mouse_event(source, up_event_type, current_location, cg_button)
            .map_err(|_| anyhow::anyhow!("无法创建鼠标释放事件"))?;

        // 发送事件 - 尝试多种发送位置
        log::debug!("发送鼠标按下事件");
        down_event.post(core_graphics::event::CGEventTapLocation::HID);

        // 添加短暂延迟
        std::thread::sleep(std::time::Duration::from_millis(50));

        log::debug!("发送鼠标释放事件");
        up_event.post(core_graphics::event::CGEventTapLocation::HID);

        // 也尝试发送到AnnotatedSession位置
        std::thread::sleep(std::time::Duration::from_millis(10));
        down_event.post(core_graphics::event::CGEventTapLocation::AnnotatedSession);
        std::thread::sleep(std::time::Duration::from_millis(50));
        up_event.post(core_graphics::event::CGEventTapLocation::AnnotatedSession);

        log::debug!(
            "macOS点击鼠标按钮: {:?} 在位置 ({}, {})",
            button,
            current_location.x,
            current_location.y
        );
        Ok(())
    }
}
