# Acto - 桌面自动化软件

Acto 是一个基于 Rust 和 Makepad GUI 框架开发的桌面自动化软件，提供强大的鼠标键盘控制、窗口管理、脚本录制回放等功能。

## 功能特性

### 🎯 核心功能
- **鼠标控制**: 点击、移动、拖拽、滚轮操作
- **键盘控制**: 按键、组合键、文本输入
- **窗口管理**: 查找、聚焦、移动、调整大小、最大化/最小化
- **脚本录制**: 实时录制用户操作并生成可执行脚本
- **脚本回放**: 精确回放录制的操作序列

### 📋 任务调度
- **定时任务**: 支持一次性、间隔、Cron表达式定时
- **事件触发**: 热键、文件变化、窗口事件触发
- **任务管理**: 启用/禁用、重试机制、超时控制

### 🎨 用户界面
- **现代化界面**: 基于 Makepad 的高性能 GUI
- **脚本编辑器**: 语法高亮、智能提示
- **任务监控**: 实时查看任务执行状态
- **配置管理**: 灵活的配置选项

### 🔧 高级功能
- **热键支持**: 全局热键快速触发脚本
- **条件控制**: if/else、循环、变量支持
- **错误处理**: 异常捕获、重试机制
- **日志记录**: 详细的执行日志和调试信息

## 快速开始

### 环境要求
- Rust 1.70+
- 支持的操作系统: Windows 10+, macOS 10.15+, Linux (X11)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/acto.git
cd acto
```

2. **构建项目**
```bash
cargo build --release
```

3. **运行应用**
```bash
cargo run
```

### 基本使用

#### 1. 录制脚本
1. 点击"录制脚本"按钮
2. 执行需要自动化的操作
3. 点击"停止录制"保存脚本

#### 2. 播放脚本
1. 在脚本列表中选择要执行的脚本
2. 点击"播放脚本"按钮
3. 脚本将自动执行录制的操作

#### 3. 创建定时任务
1. 在任务管理面板点击"新建任务"
2. 选择要执行的脚本
3. 设置触发条件（时间、热键等）
4. 启用任务

## 项目结构

```
src/
├── app.rs              # 主应用程序
├── automation/         # 自动化引擎
│   ├── mod.rs          # 自动化核心逻辑
│   ├── input.rs        # 鼠标键盘控制
│   └── window.rs       # 窗口管理
├── gui/                # 用户界面
│   └── mod.rs          # Makepad GUI 组件
├── script/             # 脚本管理
│   └── mod.rs          # 脚本存储和执行
├── task/               # 任务调度
│   └── mod.rs          # 任务调度器
├── utils/              # 工具模块
│   ├── mod.rs          # 通用工具函数
│   ├── config.rs       # 配置管理
│   └── hotkey.rs       # 热键管理
├── lib.rs              # 库入口
└── main.rs             # 程序入口
```

## 配置说明

应用程序配置文件位于：
- Windows: `%APPDATA%/Acto/config.json`
- macOS: `~/Library/Application Support/Acto/config.json`
- Linux: `~/.acto/config.json`

### 主要配置项

```json
{
  "general": {
    "auto_start": false,
    "minimize_to_tray": true,
    "language": "zh-CN"
  },
  "automation": {
    "default_delay": 100,
    "max_execution_time": 3600,
    "enable_hotkeys": true
  },
  "ui": {
    "theme": "dark",
    "window_width": 1200,
    "window_height": 800
  }
}
```

## 脚本格式

脚本使用 JSON 格式存储，支持以下操作类型：

### 鼠标操作
```json
{
  "MouseClick": {
    "button": "Left",
    "x": 100,
    "y": 200
  }
}
```

### 键盘操作
```json
{
  "TypeText": {
    "text": "Hello World"
  }
}
```

### 窗口操作
```json
{
  "WindowFocus": {
    "title": "记事本"
  }
}
```

### 控制操作
```json
{
  "Wait": {
    "duration": "1s"
  }
}
```

## 开发指南

### 添加新的自动化操作

1. 在 `automation/mod.rs` 中的 `AutomationAction` 枚举添加新操作
2. 在 `AutomationEngine::execute_action` 中实现操作逻辑
3. 在相应的平台模块中实现具体功能

### 扩展 GUI 界面

1. 在 `gui/mod.rs` 中使用 Makepad 的 `live_design!` 宏定义界面
2. 实现相应的事件处理逻辑
3. 更新 `AppUI::handle_actions` 方法

### 添加新的任务触发器

1. 在 `task/mod.rs` 中的 `TaskTrigger` 枚举添加新触发器
2. 在 `TaskScheduler` 中实现触发逻辑
3. 更新任务调度循环

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- [Makepad](https://github.com/makepad/makepad) - 现代化的 Rust GUI 框架
- [Tokio](https://tokio.rs/) - 异步运行时
- [Serde](https://serde.rs/) - 序列化框架

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue: [GitHub Issues](https://github.com/your-username/acto/issues)
- 邮箱: <EMAIL>

---

**注意**: 本软件仅用于合法的自动化任务，请遵守相关法律法规和软件使用条款。
