{"id": "sample-001", "name": "示例自动化脚本", "description": "演示基本的鼠标键盘操作和窗口管理", "created_at": "2024-01-01T00:00:00Z", "modified_at": "2024-01-01T00:00:00Z", "tags": ["示例", "教程"], "actions": [{"Wait": {"duration": "2s"}}, {"WindowFocus": {"title": "记事本"}}, {"MouseClick": {"button": "Left", "x": 300, "y": 200}}, {"TypeText": {"text": "Hello, <PERSON><PERSON>!"}}, {"KeyCombo": {"keys": ["Ctrl", "A"]}}, {"Wait": {"duration": "1s"}}, {"KeyCombo": {"keys": ["Ctrl", "C"]}}, {"MouseClick": {"button": "Left", "x": 300, "y": 250}}, {"KeyCombo": {"keys": ["Ctrl", "V"]}}, {"Loop": {"count": 3, "actions": [{"TypeText": {"text": "\n重复文本 "}}, {"Wait": {"duration": "500ms"}}]}}, {"Screenshot": {"path": "screenshot.png"}}]}