{"sourceFile": "Cargo.toml", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1747967017705, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747974882998, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,14 +1,17 @@\n [package]\n-edition = \"2021\"\n name = \"acto\"\n version = \"0.1.0\"\n+edition = \"2024\"\n+description = \"桌面自动化工具\"\n+authors = [\"Your Name <<EMAIL>>\"]\n \n [dependencies]\n-chrono = \"0.4\"\n-clap = {version = \"4.3\", features = [\"derive\"]}\n-eframe = \"0.22.0\"\n-egui = \"0.22.0\"\n+makepad-widgets = \"0.6.0\"\n enigo = \"0.1.2\"\n-rdev = \"0.5.3\"\n-serde = {version = \"1.0\", features = [\"derive\"]}\n+serde = { version = \"1.0\", features = [\"derive\"] }\n serde_json = \"1.0\"\n+rfd = \"0.11\"\n+chrono = \"0.4\"\n+log = \"0.4\"\n+simple_logger = \"4.2\"\n+once_cell = \"1.18\"\n"}, {"date": 1747975074652, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,9 @@\n description = \"桌面自动化工具\"\n authors = [\"Your Name <<EMAIL>>\"]\n \n [dependencies]\n-makepad-widgets = \"0.6.0\"\n+makepad-widgets = \"1\"\n enigo = \"0.1.2\"\n serde = { version = \"1.0\", features = [\"derive\"] }\n serde_json = \"1.0\"\n rfd = \"0.11\"\n"}, {"date": 1747976691582, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,17 +1,6 @@\n [package]\n+edition = \"2024\"\n name = \"acto\"\n version = \"0.1.0\"\n-edition = \"2024\"\n-description = \"桌面自动化工具\"\n-authors = [\"Your Name <<EMAIL>>\"]\n \n [dependencies]\n-makepad-widgets = \"1\"\n-enigo = \"0.1.2\"\n-serde = { version = \"1.0\", features = [\"derive\"] }\n-serde_json = \"1.0\"\n-rfd = \"0.11\"\n-chrono = \"0.4\"\n-log = \"0.4\"\n-simple_logger = \"4.2\"\n-once_cell = \"1.18\"\n"}, {"date": 1747977038098, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,4 +3,13 @@\n name = \"acto\"\n version = \"0.1.0\"\n \n [dependencies]\n+enigo = \"0.2\"\n+image = \"0.24\"\n+makepad-platform = \"0.7\"\n+makepad-widgets = \"0.7\"\n+rdev = \"0.4\"\n+screenshot = \"0.4\"\n+serde = {version = \"1.0\", features = [\"derive\"]}\n+serde_json = \"1.0\"\n+tokio = {version = \"1.0\", features = [\"full\"]}\n"}, {"date": 1747977331982, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,9 @@\n name = \"acto\"\n version = \"0.1.0\"\n \n [dependencies]\n+chrono = {version = \"0.4\", features = [\"serde\"]}\n enigo = \"0.2\"\n image = \"0.24\"\n makepad-platform = \"0.7\"\n makepad-widgets = \"0.7\"\n"}, {"date": 1747978433807, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,10 +6,10 @@\n [dependencies]\n chrono = {version = \"0.4\", features = [\"serde\"]}\n enigo = \"0.2\"\n image = \"0.24\"\n-makepad-platform = \"0.7\"\n-makepad-widgets = \"0.7\"\n+makepad-platform = \"0.9\"\n+makepad-widgets = \"0.9\"\n rdev = \"0.4\"\n screenshot = \"0.4\"\n serde = {version = \"1.0\", features = [\"derive\"]}\n serde_json = \"1.0\"\n"}, {"date": 1747978513346, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,14 @@\n+[package]\n+edition = \"2024\"\n+name = \"acto\"\n+version = \"0.1.0\"\n+\n+[dependencies]\n+chrono = {version = \"0.4\", features = [\"serde\"]}\n+enigo = \"0.2\"\n+makepad-platform = \"0.9\"\n+makepad-widgets = \"0.9\"\n+rdev = \"0.4\"\n+serde = {version = \"1.0\", features = [\"derive\"]}\n+serde_json = \"1.0\"\n+tokio = {version = \"1.0\", features = [\"full\"]}\n"}, {"date": 1747979673294, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,28 +3,4 @@\n name = \"acto\"\n version = \"0.1.0\"\n \n [dependencies]\n-chrono = {version = \"0.4\", features = [\"serde\"]}\n-enigo = \"0.2\"\n-makepad-platform = \"0.9\"\n-makepad-widgets = \"0.9\"\n-rdev = \"0.4\"\n-serde = {version = \"1.0\", features = [\"derive\"]}\n-serde_json = \"1.0\"\n-tokio = {version = \"1.0\", features = [\"full\"]}\n-[package]\n-edition = \"2024\"\n-name = \"acto\"\n-version = \"0.1.0\"\n-\n-[dependencies]\n-chrono = {version = \"0.4\", features = [\"serde\"]}\n-enigo = \"0.2\"\n-image = \"0.24\"\n-makepad-platform = \"0.9\"\n-makepad-widgets = \"0.9\"\n-rdev = \"0.4\"\n-screenshot = \"0.4\"\n-serde = {version = \"1.0\", features = [\"derive\"]}\n-serde_json = \"1.0\"\n-tokio = {version = \"1.0\", features = [\"full\"]}\n"}], "date": 1747967017705, "name": "Commit-0", "content": "[package]\nedition = \"2021\"\nname = \"acto\"\nversion = \"0.1.0\"\n\n[dependencies]\nchrono = \"0.4\"\nclap = {version = \"4.3\", features = [\"derive\"]}\neframe = \"0.22.0\"\negui = \"0.22.0\"\nenigo = \"0.1.2\"\nrdev = \"0.5.3\"\nserde = {version = \"1.0\", features = [\"derive\"]}\nserde_json = \"1.0\"\n"}]}