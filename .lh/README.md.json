{"sourceFile": "README.md", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1747967210431, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747977435806, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,105 +1,151 @@\n # Acto - 桌面自动化工具\n \n-Acto 是一个用 Rust 编写的桌面自动化工具，可以记录和回放用户的鼠标和键盘操作，帮助用户自动化重复性任务。\n+一个基于 Rust 和 Makepad 框架开发的桌面自动化软件，提供直观的图形界面来录制、编辑和回放用户操作。\n \n-## 功能特点\n+## 功能特性\n \n-一个完整的自动化工具通常包含以下模块：\n+### 🎯 核心模块\n \n-- **事件捕获模块**：记录用户的鼠标点击、键盘输入、窗口切换等操作，并保存为可重复执行的脚本。\n-- **脚本生成模块**：将捕获的操作转化为可读的脚本格式（如 JSON/YAML 或自定义语法），方便编辑和共享。\n-- **回放控制模块**：解析脚本并模拟用户操作，支持循环、延时、条件判断等逻辑控制。\n-- **界面管理模块**：提供图形界面，用于录制/回放控制、脚本编辑和调试。\n+| 模块             | 功能描述                                                               |\n+| ---------------- | ---------------------------------------------------------------------- |\n+| **事件捕获模块** | 记录用户的鼠标点击、键盘输入、窗口切换等操作，并保存为可重复执行的脚本 |\n+| **脚本生成模块** | 将捕获的操作转化为可读的脚本格式（JSON 格式），支持自定义语法          |\n+| **回放控制模块** | 解析脚本并模拟用户操作，支持循环、延时、条件判断等逻辑                 |\n+| **界面管理模块** | 提供图形界面，用于录制/回放控制、脚本编辑和调试                        |\n \n-## 安装\n+### ✨ 主要功能\n \n-### 从源码编译\n+- **🔴 开始录制**: 捕获鼠标移动、点击和键盘输入\n+- **⏹️ 停止录制**: 结束录制并生成脚本\n+- **▶️ 播放脚本**: 自动执行录制的操作序列\n+- **💾 保存脚本**: 将脚本保存到本地文件\n+- **📂 加载脚本**: 从文件加载之前保存的脚本\n+- **✏️ 脚本编辑**: 在界面中直接编辑脚本内容\n \n-确保您已安装 Rust 工具链，然后执行：\n+## 技术栈\n \n+- **GUI 框架**: [Makepad](https://github.com/makepad/makepad) - 现代化的 Rust GUI 框架\n+- **事件捕获**: [rdev](https://github.com/Narsil/rdev) - 跨平台输入事件监听\n+- **自动化控制**: [enigo](https://github.com/enigo-rs/enigo) - 跨平台输入模拟\n+- **序列化**: [serde](https://serde.rs/) - JSON 脚本格式支持\n+- **异步运行时**: [tokio](https://tokio.rs/) - 异步任务处理\n+\n+## 安装和运行\n+\n+### 前置要求\n+\n+- Rust 1.70+\n+- macOS / Linux / Windows\n+\n+### 构建项目\n+\n ```bash\n-git clone https://github.com/yourusername/acto.git\n+# 克隆项目\n+git clone <repository-url>\n cd acto\n+\n+# 构建项目\n cargo build --release\n+\n+# 运行应用\n+cargo run\n ```\n \n-编译后的可执行文件将位于`target/release/`目录中。\n+## 使用指南\n \n-## 使用方法\n+### 1. 录制操作\n \n-### 命令行模式\n+1. 点击 **\"开始录制\"** 按钮\n+2. 执行你想要自动化的操作（鼠标点击、键盘输入等）\n+3. 点击 **\"停止录制\"** 按钮\n+4. 录制的脚本将显示在文本区域中\n \n-#### 捕获事件\n+### 2. 编辑脚本\n \n-```bash\n-# 捕获事件并保存到output.json（按ESC键停止）\n-acto capture -o output.json\n+录制完成后，你可以在脚本文本区域中手动编辑脚本内容。脚本采用 JSON 格式：\n \n-# 捕获事件60秒\n-acto capture -o output.json -d 60\n+```json\n+[\n+  {\n+    \"MouseMove\": {\n+      \"x\": 100,\n+      \"y\": 200,\n+      \"delay\": 500\n+    }\n+  },\n+  {\n+    \"MouseClick\": {\n+      \"button\": \"Left\",\n+      \"x\": 100,\n+      \"y\": 200,\n+      \"delay\": 100\n+    }\n+  },\n+  {\n+    \"KeyPress\": {\n+      \"key\": \"Hello\",\n+      \"delay\": 50\n+    }\n+  }\n+]\n ```\n \n-#### 回放脚本\n+### 3. 执行脚本\n \n-```bash\n-# 以正常速度回放脚本\n-acto playback -s script.json\n+1. 确保脚本内容正确\n+2. 点击 **\"播放脚本\"** 按钮\n+3. 程序将自动执行脚本中的所有操作\n \n-# 以2倍速回放脚本\n-acto playback -s script.json -s 2.0\n-```\n+### 4. 保存和加载\n \n-### 图形界面模式\n+- **保存脚本**: 点击 \"保存脚本\" 将当前脚本保存到 `scripts/` 目录\n+- **加载脚本**: 点击 \"加载脚本\" 加载最近保存的脚本文件\n \n-```bash\n-acto gui\n-```\n+## 脚本格式说明\n \n-或直接运行不带参数的命令：\n+### 支持的操作类型\n \n-```bash\n-acto\n-```\n+| 操作类型     | 描述     | 参数                        |\n+| ------------ | -------- | --------------------------- |\n+| `MouseMove`  | 鼠标移动 | `x`, `y`, `delay`           |\n+| `MouseClick` | 鼠标点击 | `button`, `x`, `y`, `delay` |\n+| `KeyPress`   | 按键按下 | `key`, `delay`              |\n+| `KeyRelease` | 按键释放 | `key`, `delay`              |\n+| `Wait`       | 等待     | `duration`                  |\n \n-## 脚本格式\n+### 参数说明\n \n-Acto 使用 JSON 格式存储捕获的事件，基本结构如下：\n+- `x`, `y`: 屏幕坐标（像素）\n+- `delay`: 延时（毫秒）\n+- `button`: 鼠标按钮（`\"Left\"`, `\"Right\"`, `\"Middle\"`）\n+- `key`: 按键字符串\n+- `duration`: 等待时长（毫秒）\n \n-```json\n-{\n-  \"events\": [\n-    {\n-      \"timestamp\": 0,\n-      \"event_type\": \"Mouse\",\n-      \"action\": \"Move\",\n-      \"detail\": \"\",\n-      \"x\": 100,\n-      \"y\": 200\n-    },\n-    {\n-      \"timestamp\": 500,\n-      \"event_type\": \"Mouse\",\n-      \"action\": \"Press\",\n-      \"detail\": \"Left\",\n-      \"x\": null,\n-      \"y\": null\n-    }\n-  ],\n-  \"created_at\": \"2023-05-20T15:30:00Z\",\n-  \"duration\": 500\n-}\n+## 项目结构\n+\n ```\n+acto/\n+├── src/\n+│   ├── main.rs              # 主程序和 GUI 界面\n+│   └── automation_engine.rs # 自动化引擎核心逻辑\n+├── scripts/                 # 保存的脚本文件目录\n+├── Cargo.toml              # 项目配置和依赖\n+└── README.md               # 项目说明文档\n+```\n \n-## 注意事项\n+## 开发计划\n \n-- 在某些操作系统上，可能需要特殊权限才能捕获和模拟系统级别的输入事件。\n-- 回放脚本时，请确保您的屏幕分辨率与录制时相同，否则鼠标位置可能不准确。\n-- 建议在回放前先备份重要数据，以防意外操作。\n+- [ ] 添加更多的事件类型支持（窗口操作、文件操作等）\n+- [ ] 实现条件判断和循环控制\n+- [ ] 添加脚本调试功能\n+- [ ] 支持图像识别和 OCR\n+- [ ] 添加定时任务功能\n+- [ ] 优化用户界面和用户体验\n \n-## 许可证\n+## 贡献\n \n-本项目采用 MIT 许可证。详见[LICENSE](LICENSE)文件。\n+欢迎提交 Issue 和 Pull Request 来改进这个项目！\n \n-## 贡献\n+## 许可证\n \n-欢迎提交问题报告和拉取请求！\n+MIT License\n"}], "date": 1747967210431, "name": "Commit-0", "content": "# Acto - 桌面自动化工具\n\nActo 是一个用 Rust 编写的桌面自动化工具，可以记录和回放用户的鼠标和键盘操作，帮助用户自动化重复性任务。\n\n## 功能特点\n\n一个完整的自动化工具通常包含以下模块：\n\n- **事件捕获模块**：记录用户的鼠标点击、键盘输入、窗口切换等操作，并保存为可重复执行的脚本。\n- **脚本生成模块**：将捕获的操作转化为可读的脚本格式（如 JSON/YAML 或自定义语法），方便编辑和共享。\n- **回放控制模块**：解析脚本并模拟用户操作，支持循环、延时、条件判断等逻辑控制。\n- **界面管理模块**：提供图形界面，用于录制/回放控制、脚本编辑和调试。\n\n## 安装\n\n### 从源码编译\n\n确保您已安装 Rust 工具链，然后执行：\n\n```bash\ngit clone https://github.com/yourusername/acto.git\ncd acto\ncargo build --release\n```\n\n编译后的可执行文件将位于`target/release/`目录中。\n\n## 使用方法\n\n### 命令行模式\n\n#### 捕获事件\n\n```bash\n# 捕获事件并保存到output.json（按ESC键停止）\nacto capture -o output.json\n\n# 捕获事件60秒\nacto capture -o output.json -d 60\n```\n\n#### 回放脚本\n\n```bash\n# 以正常速度回放脚本\nacto playback -s script.json\n\n# 以2倍速回放脚本\nacto playback -s script.json -s 2.0\n```\n\n### 图形界面模式\n\n```bash\nacto gui\n```\n\n或直接运行不带参数的命令：\n\n```bash\nacto\n```\n\n## 脚本格式\n\nActo 使用 JSON 格式存储捕获的事件，基本结构如下：\n\n```json\n{\n  \"events\": [\n    {\n      \"timestamp\": 0,\n      \"event_type\": \"Mouse\",\n      \"action\": \"Move\",\n      \"detail\": \"\",\n      \"x\": 100,\n      \"y\": 200\n    },\n    {\n      \"timestamp\": 500,\n      \"event_type\": \"Mouse\",\n      \"action\": \"Press\",\n      \"detail\": \"Left\",\n      \"x\": null,\n      \"y\": null\n    }\n  ],\n  \"created_at\": \"2023-05-20T15:30:00Z\",\n  \"duration\": 500\n}\n```\n\n## 注意事项\n\n- 在某些操作系统上，可能需要特殊权限才能捕获和模拟系统级别的输入事件。\n- 回放脚本时，请确保您的屏幕分辨率与录制时相同，否则鼠标位置可能不准确。\n- 建议在回放前先备份重要数据，以防意外操作。\n\n## 许可证\n\n本项目采用 MIT 许可证。详见[LICENSE](LICENSE)文件。\n\n## 贡献\n\n欢迎提交问题报告和拉取请求！\n"}]}