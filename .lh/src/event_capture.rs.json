{"sourceFile": "src/event_capture.rs", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1747967061381, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747967376241, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,9 +6,9 @@\n use std::sync::{Arc, Mutex};\n use std::thread;\n use std::time::{Duration, Instant, SystemTime};\n \n-#[derive(Debug, Serialize, Deserialize)]\n+#[derive(Debug, Clone, Serialize, Deserialize)]\n pub struct RecordedEvent {\n     pub timestamp: u128,    // 事件发生的时间戳（毫秒）\n     pub event_type: String, // 事件类型（键盘、鼠标等）\n     pub action: String,     // 动作（按下、释放等）\n@@ -16,16 +16,16 @@\n     pub x: Option<f64>,     // 鼠标X坐标\n     pub y: Option<f64>,     // 鼠标Y坐标\n }\n \n-#[derive(Debug, Serialize, Deserialize)]\n+#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]\n pub struct Script {\n     pub events: Vec<RecordedEvent>,\n     pub created_at: String,\n     pub duration: u64, // 脚本总时长（毫秒）\n }\n \n-pub fn start_capture(output_path: PathBuf, duration_secs: Option<u64>) {\n+pub fn capture_events(output_path: PathBuf, duration_secs: u64) -> Result<(), String> {\n     println!(\"开始捕获事件，按ESC键停止...\");\n \n     let events = Arc::new(Mutex::new(Vec::new()));\n     let start_time = Instant::now();\n@@ -35,12 +35,12 @@\n         .as_millis();\n \n     // 如果设置了持续时间，创建一个线程在指定时间后停止捕获\n     let should_stop = Arc::new(Mutex::new(false));\n-    if let Some(duration) = duration_secs {\n+    if duration_secs > 0 {\n         let should_stop_clone = should_stop.clone();\n         thread::spawn(move || {\n-            thread::sleep(Duration::from_secs(duration));\n+            thread::sleep(Duration::from_secs(duration_secs));\n             *should_stop_clone.lock().unwrap() = true;\n             println!(\"达到指定的捕获时长，停止捕获\");\n         });\n     }\n@@ -142,10 +142,9 @@\n     });\n \n     // 检查监听是否成功\n     if let Err(error) = callback_result {\n-        eprintln!(\"监听事件失败: {:?}\", error);\n-        return;\n+        return Err(format!(\"监听事件失败: {:?}\", error));\n     }\n \n     // 计算捕获持续时间\n     let duration = start_time.elapsed().as_millis() as u64;\n@@ -158,17 +157,32 @@\n     };\n \n     // 保存到文件\n     save_script(&script, output_path);\n+\n+    Ok(())\n }\n \n-fn save_script(script: &Script, path: PathBuf) {\n-    let json = serde_json::to_string_pretty(script).unwrap();\n-    let mut file = File::create(path.clone()).unwrap();\n-    file.write_all(json.as_bytes()).unwrap();\n+fn save_script(script: &Script, path: PathBuf) -> Result<(), String> {\n+    let json = match serde_json::to_string_pretty(script) {\n+        Ok(json) => json,\n+        Err(e) => return Err(format!(\"序列化脚本失败: {}\", e)),\n+    };\n+\n+    let mut file = match File::create(path.clone()) {\n+        Ok(file) => file,\n+        Err(e) => return Err(format!(\"创建文件失败: {}\", e)),\n+    };\n+\n+    if let Err(e) = file.write_all(json.as_bytes()) {\n+        return Err(format!(\"写入文件失败: {}\", e));\n+    }\n+\n     println!(\"脚本已保存到: {:?}\", path);\n     println!(\n         \"共记录 {} 个事件，总时长: {}ms\",\n         script.events.len(),\n         script.duration\n     );\n+\n+    Ok(())\n }\n"}, {"date": 1747967459331, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -156,9 +156,9 @@\n         duration,\n     };\n \n     // 保存到文件\n-    save_script(&script, output_path);\n+    save_script(&script, output_path)?;\n \n     Ok(())\n }\n \n"}, {"date": 1747974918513, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -179,5 +179,5 @@\n             // 其他键的映射...\n             _ => error!(\"不支持的键: {:?}\", key),\n         }\n     }\n-}\n+}\n\\ No newline at end of file\n"}], "date": 1747967061381, "name": "Commit-0", "content": "use rdev::{listen, Event, EventType, Key};\nuse serde::{Deserialize, Serialize};\nuse std::fs::File;\nuse std::io::Write;\nuse std::path::PathBuf;\nuse std::sync::{Arc, Mutex};\nuse std::thread;\nuse std::time::{Duration, Instant, SystemTime};\n\n#[derive(Debug, Serialize, Deserialize)]\npub struct RecordedEvent {\n    pub timestamp: u128,    // 事件发生的时间戳（毫秒）\n    pub event_type: String, // 事件类型（键盘、鼠标等）\n    pub action: String,     // 动作（按下、释放等）\n    pub detail: String,     // 详细信息（按键名、鼠标坐标等）\n    pub x: Option<f64>,     // 鼠标X坐标\n    pub y: Option<f64>,     // 鼠标Y坐标\n}\n\n#[derive(Debug, Serialize, Deserialize)]\npub struct Script {\n    pub events: Vec<RecordedEvent>,\n    pub created_at: String,\n    pub duration: u64, // 脚本总时长（毫秒）\n}\n\npub fn start_capture(output_path: PathBuf, duration_secs: Option<u64>) {\n    println!(\"开始捕获事件，按ESC键停止...\");\n\n    let events = Arc::new(Mutex::new(Vec::new()));\n    let start_time = Instant::now();\n    let start_timestamp = SystemTime::now()\n        .duration_since(SystemTime::UNIX_EPOCH)\n        .unwrap()\n        .as_millis();\n\n    // 如果设置了持续时间，创建一个线程在指定时间后停止捕获\n    let should_stop = Arc::new(Mutex::new(false));\n    if let Some(duration) = duration_secs {\n        let should_stop_clone = should_stop.clone();\n        thread::spawn(move || {\n            thread::sleep(Duration::from_secs(duration));\n            *should_stop_clone.lock().unwrap() = true;\n            println!(\"达到指定的捕获时长，停止捕获\");\n        });\n    }\n\n    // 克隆用于回调函数\n    let events_clone = events.clone();\n    let should_stop_clone = should_stop.clone();\n\n    // 开始监听事件\n    let callback_result = listen(move |event: Event| {\n        // 检查是否应该停止\n        if *should_stop_clone.lock().unwrap() {\n            return;\n        }\n\n        // 计算相对时间戳（毫秒）\n        let timestamp = SystemTime::now()\n            .duration_since(SystemTime::UNIX_EPOCH)\n            .unwrap()\n            .as_millis()\n            - start_timestamp;\n\n        // 处理事件\n        match event.event_type {\n            EventType::KeyPress(key) => {\n                // 如果按下ESC键，停止捕获\n                if key == Key::Escape {\n                    *should_stop_clone.lock().unwrap() = true;\n                    println!(\"检测到ESC键，停止捕获\");\n                    return;\n                }\n\n                let recorded = RecordedEvent {\n                    timestamp,\n                    event_type: \"Keyboard\".to_string(),\n                    action: \"Press\".to_string(),\n                    detail: format!(\"{:?}\", key),\n                    x: None,\n                    y: None,\n                };\n                events_clone.lock().unwrap().push(recorded);\n            }\n            EventType::KeyRelease(key) => {\n                let recorded = RecordedEvent {\n                    timestamp,\n                    event_type: \"Keyboard\".to_string(),\n                    action: \"Release\".to_string(),\n                    detail: format!(\"{:?}\", key),\n                    x: None,\n                    y: None,\n                };\n                events_clone.lock().unwrap().push(recorded);\n            }\n            EventType::ButtonPress(button) => {\n                let recorded = RecordedEvent {\n                    timestamp,\n                    event_type: \"Mouse\".to_string(),\n                    action: \"Press\".to_string(),\n                    detail: format!(\"{:?}\", button),\n                    x: None,\n                    y: None,\n                };\n                events_clone.lock().unwrap().push(recorded);\n            }\n            EventType::ButtonRelease(button) => {\n                let recorded = RecordedEvent {\n                    timestamp,\n                    event_type: \"Mouse\".to_string(),\n                    action: \"Release\".to_string(),\n                    detail: format!(\"{:?}\", button),\n                    x: None,\n                    y: None,\n                };\n                events_clone.lock().unwrap().push(recorded);\n            }\n            EventType::MouseMove { x, y } => {\n                let recorded = RecordedEvent {\n                    timestamp,\n                    event_type: \"Mouse\".to_string(),\n                    action: \"Move\".to_string(),\n                    detail: \"\".to_string(),\n                    x: Some(x),\n                    y: Some(y),\n                };\n                events_clone.lock().unwrap().push(recorded);\n            }\n            EventType::Wheel { delta_x, delta_y } => {\n                let recorded = RecordedEvent {\n                    timestamp,\n                    event_type: \"Mouse\".to_string(),\n                    action: \"Wheel\".to_string(),\n                    detail: format!(\"delta_x: {}, delta_y: {}\", delta_x, delta_y),\n                    x: None,\n                    y: None,\n                };\n                events_clone.lock().unwrap().push(recorded);\n            }\n        }\n    });\n\n    // 检查监听是否成功\n    if let Err(error) = callback_result {\n        eprintln!(\"监听事件失败: {:?}\", error);\n        return;\n    }\n\n    // 计算捕获持续时间\n    let duration = start_time.elapsed().as_millis() as u64;\n\n    // 创建脚本对象\n    let script = Script {\n        events: events.lock().unwrap().clone(),\n        created_at: chrono::Local::now().to_rfc3339(),\n        duration,\n    };\n\n    // 保存到文件\n    save_script(&script, output_path);\n}\n\nfn save_script(script: &Script, path: PathBuf) {\n    let json = serde_json::to_string_pretty(script).unwrap();\n    let mut file = File::create(path.clone()).unwrap();\n    file.write_all(json.as_bytes()).unwrap();\n    println!(\"脚本已保存到: {:?}\", path);\n    println!(\n        \"共记录 {} 个事件，总时长: {}ms\",\n        script.events.len(),\n        script.duration\n    );\n}\n"}]}