{"sourceFile": "src/gui.rs", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1747967189236, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747967295977, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,19 +1,30 @@\n-use crate::event_capture::{self, <PERSON>ript};\n-use crate::playback_control;\n-use crate::script_generator;\n-use eframe::{egui, epi};\n-use egui::{Color32, RichText, Ui};\n+use eframe::egui;\n use std::path::PathBuf;\n use std::sync::{Arc, Mutex};\n use std::thread;\n \n+use crate::event_capture::{capture_events, Script};\n+use crate::playback_control::playback_script;\n+use crate::script_generator;\n+\n pub struct ActoApp {\n     current_tab: Tab,\n-    capture_state: CaptureState,\n-    playback_state: PlaybackState,\n-    script_state: ScriptState,\n     status_message: String,\n+    // 捕获相关\n+    capture_output_path: String,\n+    capture_duration: String,\n+    is_capturing: bool,\n+    capture_thread: Option<thread::JoinHandle<()>>,\n+    // 回放相关\n+    playback_script_path: String,\n+    playback_speed: String,\n+    is_playing: bool,\n+    playback_thread: Option<thread::JoinHandle<()>>,\n+    // 脚本编辑相关\n+    script_edit_path: String,\n+    script_content: String,\n+    script: Option<Script>,\n }\n \n #[derive(PartialEq)]\n enum Tab {\n@@ -22,100 +33,57 @@\n     ScriptEdit,\n     Settings,\n }\n \n-struct CaptureState {\n-    is_capturing: bool,\n-    output_path: String,\n-    duration_secs: String,\n-    capture_thread: Option<std::thread::JoinHandle<()>>,\n-    should_stop: Arc<Mutex<bool>>,\n-}\n-\n-struct PlaybackState {\n-    script_path: String,\n-    speed_factor: f64,\n-    is_playing: bool,\n-    playback_thread: Option<std::thread::JoinHandle<()>>,\n-}\n-\n-struct ScriptState {\n-    current_script: Option<Script>,\n-    script_path: String,\n-    script_text: String,\n-    is_modified: bool,\n-}\n-\n impl Default for ActoApp {\n     fn default() -> Self {\n         Self {\n             current_tab: Tab::Capture,\n-            capture_state: CaptureState {\n-                is_capturing: false,\n-                output_path: \"output.json\".to_string(),\n-                duration_secs: \"\".to_string(),\n-                capture_thread: None,\n-                should_stop: Arc::new(Mutex::new(false)),\n-            },\n-            playback_state: PlaybackState {\n-                script_path: \"\".to_string(),\n-                speed_factor: 1.0,\n-                is_playing: false,\n-                playback_thread: None,\n-            },\n-            script_state: ScriptState {\n-                current_script: None,\n-                script_path: \"\".to_string(),\n-                script_text: \"\".to_string(),\n-                is_modified: false,\n-            },\n             status_message: \"就绪\".to_string(),\n+            capture_output_path: \"output.json\".to_string(),\n+            capture_duration: \"0\".to_string(),\n+            is_capturing: false,\n+            capture_thread: None,\n+            playback_script_path: \"script.json\".to_string(),\n+            playback_speed: \"1.0\".to_string(),\n+            is_playing: false,\n+            playback_thread: None,\n+            script_edit_path: \"\".to_string(),\n+            script_content: \"\".to_string(),\n+            script: None,\n         }\n     }\n }\n \n-impl epi::App for ActoApp {\n-    fn name(&self) -> &str {\n-        \"Acto - 桌面自动化工具\"\n-    }\n-\n-    fn update(&mut self, ctx: &egui::Context, _frame: &epi::Frame) {\n+impl eframe::App for ActoApp {\n+    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {\n+        // 顶部菜单栏\n         egui::TopBottomPanel::top(\"top_panel\").show(ctx, |ui| {\n             egui::menu::bar(ui, |ui| {\n                 ui.menu_button(\"文件\", |ui| {\n-                    if ui.button(\"打开脚本\").clicked() {\n-                        // 这里应该打开文件对话框，但egui目前不直接支持\n-                        // 可以使用native_dialog或rfd库实现\n-                        ui.close_menu();\n-                    }\n-                    if ui.button(\"保存脚本\").clicked() {\n-                        // 保存当前脚本\n-                        ui.close_menu();\n-                    }\n                     if ui.button(\"退出\").clicked() {\n                         std::process::exit(0);\n                     }\n                 });\n                 ui.menu_button(\"帮助\", |ui| {\n                     if ui.button(\"关于\").clicked() {\n                         self.status_message = \"Acto - 桌面自动化工具 v0.1.0\".to_string();\n-                        ui.close_menu();\n                     }\n                 });\n             });\n         });\n \n+        // 底部状态栏\n         egui::TopBottomPanel::bottom(\"bottom_panel\").show(ctx, |ui| {\n             ui.horizontal(|ui| {\n-                ui.label(\"状态: \");\n-                ui.label(&self.status_message);\n+                ui.label(format!(\"状态: {}\", self.status_message));\n             });\n         });\n \n+        // 左侧导航栏\n         egui::SidePanel::left(\"side_panel\").show(ctx, |ui| {\n-            ui.heading(\"功能导航\");\n+            ui.heading(\"功能\");\n             ui.separator();\n-\n             if ui\n                 .selectable_label(self.current_tab == Tab::Capture, \"事件捕获\")\n                 .clicked()\n             {\n@@ -140,280 +108,262 @@\n                 self.current_tab = Tab::Settings;\n             }\n         });\n \n+        // 中央内容面板\n         egui::CentralPanel::default().show(ctx, |ui| match self.current_tab {\n-            Tab::Capture => self.render_capture_tab(ui),\n-            Tab::Playback => self.render_playback_tab(ui),\n-            Tab::ScriptEdit => self.render_script_edit_tab(ui),\n-            Tab::Settings => self.render_settings_tab(ui),\n+            Tab::Capture => self.show_capture_tab(ui),\n+            Tab::Playback => self.show_playback_tab(ui),\n+            Tab::ScriptEdit => self.show_script_edit_tab(ui),\n+            Tab::Settings => self.show_settings_tab(ui),\n         });\n     }\n }\n \n impl ActoApp {\n-    fn render_capture_tab(&mut self, ui: &mut Ui) {\n+    // 事件捕获选项卡\n+    fn show_capture_tab(&mut self, ui: &mut egui::Ui) {\n         ui.heading(\"事件捕获\");\n         ui.separator();\n \n         ui.horizontal(|ui| {\n-            ui.label(\"输出文件路径: \");\n-            ui.text_edit_singleline(&mut self.capture_state.output_path);\n+            ui.label(\"输出文件路径:\");\n+            ui.text_edit_singleline(&mut self.capture_output_path);\n         });\n \n         ui.horizontal(|ui| {\n-            ui.label(\"捕获时长(秒): \");\n-            ui.text_edit_singleline(&mut self.capture_state.duration_secs);\n-            ui.label(\"(留空表示无限制，按ESC停止)\");\n+            ui.label(\"捕获时长(秒, 0表示无限制):\");\n+            ui.text_edit_singleline(&mut self.capture_duration);\n         });\n \n-        ui.add_space(10.0);\n-\n-        if !self.capture_state.is_capturing {\n-            if ui.button(\"开始捕获\").clicked() {\n-                self.start_capture();\n+        ui.horizontal(|ui| {\n+            if !self.is_capturing {\n+                if ui.button(\"开始捕获\").clicked() {\n+                    self.start_capture();\n+                }\n+            } else {\n+                if ui.button(\"停止捕获\").clicked() {\n+                    self.stop_capture();\n+                }\n             }\n-        } else {\n-            if ui.button(\"停止捕获\").clicked() {\n-                self.stop_capture();\n-            }\n-        }\n+        });\n \n-        ui.add_space(20.0);\n-        ui.label(RichText::new(\"使用说明:\").color(Color32::YELLOW));\n-        ui.label(\"1. 设置输出文件路径和可选的捕获时长\");\n-        ui.label(\"2. 点击'开始捕获'按钮\");\n-        ui.label(\"3. 执行您想要自动化的操作\");\n-        ui.label(\"4. 按ESC键或点击'停止捕获'按钮结束捕获\");\n+        ui.add_space(10.0);\n+        ui.label(\"提示: 捕获过程中按ESC键可以停止捕获\");\n     }\n \n-    fn render_playback_tab(&mut self, ui: &mut Ui) {\n+    // 脚本回放选项卡\n+    fn show_playback_tab(&mut self, ui: &mut egui::Ui) {\n         ui.heading(\"脚本回放\");\n         ui.separator();\n \n         ui.horizontal(|ui| {\n-            ui.label(\"脚本文件路径: \");\n-            ui.text_edit_singleline(&mut self.playback_state.script_path);\n-            if ui.button(\"浏览...\").clicked() {\n-                // 这里应该打开文件对话框\n-            }\n+            ui.label(\"脚本文件路径:\");\n+            ui.text_edit_singleline(&mut self.playback_script_path);\n         });\n \n         ui.horizontal(|ui| {\n-            ui.label(\"回放速度: \");\n-            ui.add(\n-                egui::Slider::new(&mut self.playback_state.speed_factor, 0.1..=5.0).text(\"倍速\"),\n-            );\n+            ui.label(\"回放速度(倍数):\");\n+            ui.text_edit_singleline(&mut self.playback_speed);\n         });\n \n-        ui.add_space(10.0);\n-\n-        if !self.playback_state.is_playing {\n-            if ui.button(\"开始回放\").clicked() {\n-                self.start_playback();\n-            }\n-        } else {\n-            if ui.button(\"停止回放\").clicked() {\n+        ui.horizontal(|ui| {\n+            if !self.is_playing {\n+                if ui.button(\"开始回放\").clicked() {\n+                    self.start_playback();\n+                }\n+            } else {\n                 // 目前没有实现停止回放的功能\n-                self.status_message = \"回放停止功能尚未实现\".to_string();\n+                ui.label(\"回放中...\");\n             }\n-        }\n+        });\n \n-        ui.add_space(20.0);\n-        ui.label(RichText::new(\"使用说明:\").color(Color32::YELLOW));\n-        ui.label(\"1. 选择要回放的脚本文件\");\n-        ui.label(\"2. 调整回放速度（1.0为正常速度）\");\n-        ui.label(\"3. 点击'开始回放'按钮\");\n-        ui.label(\"4. 回放将在3秒倒计时后开始\");\n+        ui.add_space(10.0);\n+        ui.label(\"提示: 回放开始前会有3秒倒计时\");\n     }\n \n-    fn render_script_edit_tab(&mut self, ui: &mut Ui) {\n+    // 脚本编辑选项卡\n+    fn show_script_edit_tab(&mut self, ui: &mut egui::Ui) {\n         ui.heading(\"脚本编辑\");\n         ui.separator();\n \n         ui.horizontal(|ui| {\n-            ui.label(\"脚本文件: \");\n-            ui.text_edit_singleline(&mut self.script_state.script_path);\n-\n+            ui.label(\"脚本文件路径:\");\n+            ui.text_edit_singleline(&mut self.script_edit_path);\n             if ui.button(\"加载\").clicked() {\n                 self.load_script();\n             }\n-\n             if ui.button(\"保存\").clicked() {\n                 self.save_script();\n             }\n         });\n \n-        ui.add_space(10.0);\n-\n-        if let Some(script) = &self.script_state.current_script {\n-            ui.label(format!(\"事件数量: {}\", script.events.len()));\n-            ui.label(format!(\"总时长: {}毫秒\", script.duration));\n-\n-            ui.add_space(10.0);\n-\n+        ui.horizontal(|ui| {\n             if ui.button(\"生成可读文本\").clicked() {\n-                self.script_state.script_text = script_generator::script_to_readable_text(script);\n+                self.generate_readable_text();\n             }\n-\n-            if ui.button(\"调整速度\").clicked() {\n-                if let Some(script) = &self.script_state.current_script {\n-                    let adjusted = script_generator::adjust_script_speed(\n-                        script,\n-                        self.playback_state.speed_factor,\n-                    );\n-                    self.script_state.current_script = Some(adjusted);\n-                    self.script_state.is_modified = true;\n-                    self.status_message =\n-                        format!(\"脚本速度已调整为{}倍\", self.playback_state.speed_factor);\n-                }\n+            if ui.button(\"调整脚本速度\").clicked() {\n+                self.adjust_script_speed();\n             }\n-        } else {\n-            ui.label(\"未加载脚本\");\n-        }\n+        });\n \n         ui.add_space(10.0);\n \n-        // 显示脚本文本\n+        // 脚本内容编辑区\n         egui::ScrollArea::vertical().show(ui, |ui| {\n             ui.add(\n-                egui::TextEdit::multiline(&mut self.script_state.script_text)\n+                egui::TextEdit::multiline(&mut self.script_content)\n                     .desired_width(f32::INFINITY)\n-                    .desired_rows(20)\n-                    .code_editor()\n-                    .lock_focus(true)\n-                    .hint_text(\"脚本内容将显示在这里\"),\n+                    .desired_rows(20),\n             );\n         });\n     }\n \n-    fn render_settings_tab(&mut self, ui: &mut Ui) {\n+    // 设置选项卡\n+    fn show_settings_tab(&mut self, ui: &mut egui::Ui) {\n         ui.heading(\"设置\");\n         ui.separator();\n-\n-        ui.label(\"此版本暂无可配置选项\");\n+        ui.label(\"暂无可配置选项\");\n     }\n \n+    // 开始捕获事件\n     fn start_capture(&mut self) {\n-        if self.capture_state.is_capturing {\n+        if self.is_capturing {\n             return;\n         }\n \n-        let output_path = PathBuf::from(&self.capture_state.output_path);\n-        let duration = if self.capture_state.duration_secs.is_empty() {\n-            None\n-        } else {\n-            match self.capture_state.duration_secs.parse::<u64>() {\n-                Ok(secs) => Some(secs),\n-                Err(_) => {\n-                    self.status_message = \"无效的捕获时长，请输入一个正整数\".to_string();\n-                    return;\n-                }\n-            }\n-        };\n+        let output_path = self.capture_output_path.clone();\n+        let duration_str = self.capture_duration.clone();\n+        let duration = duration_str.parse::<u64>().unwrap_or(0);\n \n-        // 重置停止标志\n-        let should_stop = Arc::new(Mutex::new(false));\n-        self.capture_state.should_stop = should_stop.clone();\n+        let status_message = Arc::new(Mutex::new(self.status_message.clone()));\n+        let status_clone = Arc::clone(&status_message);\n \n-        // 创建线程进行捕获\n-        let output_path_clone = output_path.clone();\n-        let handle = thread::spawn(move || {\n-            event_capture::start_capture(output_path_clone, duration);\n-        });\n+        self.capture_thread = Some(thread::spawn(move || {\n+            *status_clone.lock().unwrap() = \"正在捕获事件...\".to_string();\n+            match capture_events(PathBuf::from(output_path), duration) {\n+                Ok(_) => *status_clone.lock().unwrap() = \"事件捕获完成\".to_string(),\n+                Err(e) => *status_clone.lock().unwrap() = format!(\"捕获错误: {}\", e),\n+            }\n+        }));\n \n-        self.capture_state.capture_thread = Some(handle);\n-        self.capture_state.is_capturing = true;\n+        self.is_capturing = true;\n         self.status_message = \"正在捕获事件...\".to_string();\n     }\n \n+    // 停止捕获事件\n     fn stop_capture(&mut self) {\n-        if !self.capture_state.is_capturing {\n-            return;\n-        }\n-\n-        // 设置停止标志\n-        if let Ok(mut stop) = self.capture_state.should_stop.lock() {\n-            *stop = true;\n-        }\n-\n-        // 等待捕获线程结束\n-        if let Some(handle) = self.capture_state.capture_thread.take() {\n-            // 这里不等待线程结束，因为可能会阻塞UI\n-            // 实际应用中应该使用更复杂的机制\n-        }\n-\n-        self.capture_state.is_capturing = false;\n+        // 目前没有实现手动停止捕获的功能\n+        // 捕获会在按下ESC键或达到指定时长后自动停止\n+        self.is_capturing = false;\n         self.status_message = \"捕获已停止\".to_string();\n     }\n \n+    // 开始回放脚本\n     fn start_playback(&mut self) {\n-        if self.playback_state.is_playing {\n+        if self.is_playing {\n             return;\n         }\n \n-        let script_path = PathBuf::from(&self.playback_state.script_path);\n-        if !script_path.exists() {\n-            self.status_message = \"脚本文件不存在\".to_string();\n-            return;\n-        }\n+        let script_path = self.playback_script_path.clone();\n+        let speed_str = self.playback_speed.clone();\n+        let speed = speed_str.parse::<f64>().unwrap_or(1.0);\n \n-        let speed_factor = self.playback_state.speed_factor;\n-        let script_path_clone = script_path.clone();\n+        let status_message = Arc::new(Mutex::new(self.status_message.clone()));\n+        let status_clone = Arc::clone(&status_message);\n \n-        // 创建线程进行回放\n-        let handle = thread::spawn(move || {\n-            playback_control::start_playback(&script_path_clone, speed_factor);\n-        });\n+        self.playback_thread = Some(thread::spawn(move || {\n+            *status_clone.lock().unwrap() = \"正在回放脚本...\".to_string();\n+            match playback_script(PathBuf::from(script_path), speed) {\n+                Ok(_) => *status_clone.lock().unwrap() = \"脚本回放完成\".to_string(),\n+                Err(e) => *status_clone.lock().unwrap() = format!(\"回放错误: {}\", e),\n+            }\n+        }));\n \n-        self.playback_state.playback_thread = Some(handle);\n-        self.playback_state.is_playing = true;\n+        self.is_playing = true;\n         self.status_message = \"正在回放脚本...\".to_string();\n-\n-        // 设置一个定时器，在一段时间后检查回放是否完成\n-        // 这里简化处理，实际应用中应该使用更复杂的机制\n-        let playback_time = 5000; // 假设回放需要5秒\n-        thread::spawn(move || {\n-            thread::sleep(std::time::Duration::from_millis(playback_time));\n-            // 这里应该通知主线程回放完成\n-        });\n     }\n \n+    // 加载脚本\n     fn load_script(&mut self) {\n-        let path = PathBuf::from(&self.script_state.script_path);\n-        match script_generator::load_script(&path) {\n+        if self.script_edit_path.is_empty() {\n+            self.status_message = \"请指定脚本文件路径\".to_string();\n+            return;\n+        }\n+\n+        match script_generator::load_script(&PathBuf::from(&self.script_edit_path)) {\n             Ok(script) => {\n-                self.script_state.current_script = Some(script);\n-                self.script_state.script_text =\n-                    \"脚本加载成功，点击'生成可读文本'查看内容\".to_string();\n-                self.script_state.is_modified = false;\n-                self.status_message = \"脚本加载成功\".to_string();\n+                self.script = Some(script.clone());\n+                match serde_json::to_string_pretty(&script) {\n+                    Ok(content) => {\n+                        self.script_content = content;\n+                        self.status_message = \"脚本加载成功\".to_string();\n+                    }\n+                    Err(e) => self.status_message = format!(\"脚本格式化错误: {}\", e),\n+                }\n             }\n-            Err(e) => {\n-                self.status_message = format!(\"加载脚本失败: {}\", e);\n-            }\n+            Err(e) => self.status_message = format!(\"脚本加载错误: {}\", e),\n         }\n     }\n \n+    // 保存脚本\n     fn save_script(&mut self) {\n-        if let Some(script) = &self.script_state.current_script {\n-            let path = PathBuf::from(&self.script_state.script_path);\n-            match script_generator::save_script(script, &path) {\n-                Ok(_) => {\n-                    self.script_state.is_modified = false;\n-                    self.status_message = \"脚本保存成功\".to_string();\n+        if self.script_edit_path.is_empty() {\n+            self.status_message = \"请指定脚本文件路径\".to_string();\n+            return;\n+        }\n+\n+        match serde_json::from_str::<Script>(&self.script_content) {\n+            Ok(script) => {\n+                match script_generator::save_script(&script, &PathBuf::from(&self.script_edit_path))\n+                {\n+                    Ok(_) => self.status_message = \"脚本保存成功\".to_string(),\n+                    Err(e) => self.status_message = format!(\"脚本保存错误: {}\", e),\n                 }\n-                Err(e) => {\n-                    self.status_message = format!(\"保存脚本失败: {}\", e);\n+            }\n+            Err(e) => self.status_message = format!(\"脚本解析错误: {}\", e),\n+        }\n+    }\n+\n+    // 生成可读文本\n+    fn generate_readable_text(&mut self) {\n+        if let Some(script) = &self.script {\n+            self.script_content = script_generator::script_to_readable_text(script);\n+            self.status_message = \"已生成可读文本\".to_string();\n+        } else {\n+            self.status_message = \"请先加载脚本\".to_string();\n+        }\n+    }\n+\n+    // 调整脚本速度\n+    fn adjust_script_speed(&mut self) {\n+        if let Some(script) = &self.script {\n+            let speed_str = self.playback_speed.clone();\n+            let speed = speed_str.parse::<f64>().unwrap_or(1.0);\n+\n+            let adjusted_script = script_generator::adjust_script_speed(script, speed);\n+            self.script = Some(adjusted_script.clone());\n+\n+            match serde_json::to_string_pretty(&adjusted_script) {\n+                Ok(content) => {\n+                    self.script_content = content;\n+                    self.status_message = format!(\"脚本速度已调整为{}倍\", speed);\n                 }\n+                Err(e) => self.status_message = format!(\"脚本格式化错误: {}\", e),\n             }\n         } else {\n-            self.status_message = \"没有脚本可保存\".to_string();\n+            self.status_message = \"请先加载脚本\".to_string();\n         }\n     }\n }\n \n-pub fn start_gui() {\n-    let app = ActoApp::default();\n-    let native_options = eframe::NativeOptions::default();\n-    eframe::run_native(Box::new(app), native_options);\n+pub fn run_gui() -> Result<(), eframe::Error> {\n+    let options = eframe::NativeOptions {\n+        initial_window_size: Some(egui::vec2(800.0, 600.0)),\n+        ..Default::default()\n+    };\n+    eframe::run_native(\n+        \"Acto - 桌面自动化工具\",\n+        options,\n+        Box::new(|_cc| Box::new(ActoApp::default())),\n+    )\n }\n"}, {"date": 1747969265290, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -366,4 +366,15 @@\n         options,\n         Box::new(|_cc| Box::new(ActoApp::default())),\n     )\n }\n+\n+fn load_fonts(ctx: &egui::Context) {\n+    let mut fonts = egui::FontDefinitions::default();\n+    fonts.font_data.insert(\"my_font\".to_owned(),\n+    egui::FontData::from_static(include_bytes!(\"xxxxx.ttf\")));\n+    fonts.families.get_mut(&egui::FontFamily::Proportional).unwrap()\n+        .insert(0, \"my_font\".to_owned());\n+    fonts.families.get_mut(&egui::FontFamily::Monospace).unwrap()\n+        .push(\"my_font\".to_owned());\n+    ctx.set_fonts(fonts);\n+}\n"}, {"date": 1747969557970, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,380 @@\n+use eframe::egui;\n+use std::path::PathBuf;\n+use std::sync::{Arc, Mutex};\n+use std::thread;\n+\n+use crate::event_capture::{capture_events, Script};\n+use crate::playback_control::playback_script;\n+use crate::script_generator;\n+\n+pub struct ActoApp {\n+    current_tab: Tab,\n+    status_message: String,\n+    // 捕获相关\n+    capture_output_path: String,\n+    capture_duration: String,\n+    is_capturing: bool,\n+    capture_thread: Option<thread::Join<PERSON>andle<()>>,\n+    // 回放相关\n+    playback_script_path: String,\n+    playback_speed: String,\n+    is_playing: bool,\n+    playback_thread: Option<thread::Jo<PERSON><PERSON><PERSON><PERSON><()>>,\n+    // 脚本编辑相关\n+    script_edit_path: String,\n+    script_content: String,\n+    script: Option<Script>,\n+}\n+\n+#[derive(PartialEq)]\n+enum Tab {\n+    Capture,\n+    Playback,\n+    ScriptEdit,\n+    Settings,\n+}\n+\n+impl Default for ActoApp {\n+    fn default() -> Self {\n+        Self {\n+            current_tab: Tab::Capture,\n+            status_message: \"就绪\".to_string(),\n+            capture_output_path: \"output.json\".to_string(),\n+            capture_duration: \"0\".to_string(),\n+            is_capturing: false,\n+            capture_thread: None,\n+            playback_script_path: \"script.json\".to_string(),\n+            playback_speed: \"1.0\".to_string(),\n+            is_playing: false,\n+            playback_thread: None,\n+            script_edit_path: \"\".to_string(),\n+            script_content: \"\".to_string(),\n+            script: None,\n+        }\n+    }\n+}\n+\n+impl eframe::App for ActoApp {\n+    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {\n+        // 顶部菜单栏\n+        egui::TopBottomPanel::top(\"top_panel\").show(ctx, |ui| {\n+            egui::menu::bar(ui, |ui| {\n+                ui.menu_button(\"文件\", |ui| {\n+                    if ui.button(\"退出\").clicked() {\n+                        std::process::exit(0);\n+                    }\n+                });\n+                ui.menu_button(\"帮助\", |ui| {\n+                    if ui.button(\"关于\").clicked() {\n+                        self.status_message = \"Acto - 桌面自动化工具 v0.1.0\".to_string();\n+                    }\n+                });\n+            });\n+        });\n+\n+        // 底部状态栏\n+        egui::TopBottomPanel::bottom(\"bottom_panel\").show(ctx, |ui| {\n+            ui.horizontal(|ui| {\n+                ui.label(format!(\"状态: {}\", self.status_message));\n+            });\n+        });\n+\n+        // 左侧导航栏\n+        egui::SidePanel::left(\"side_panel\").show(ctx, |ui| {\n+            ui.heading(\"功能\");\n+            ui.separator();\n+            if ui\n+                .selectable_label(self.current_tab == Tab::Capture, \"事件捕获\")\n+                .clicked()\n+            {\n+                self.current_tab = Tab::Capture;\n+            }\n+            if ui\n+                .selectable_label(self.current_tab == Tab::Playback, \"脚本回放\")\n+                .clicked()\n+            {\n+                self.current_tab = Tab::Playback;\n+            }\n+            if ui\n+                .selectable_label(self.current_tab == Tab::ScriptEdit, \"脚本编辑\")\n+                .clicked()\n+            {\n+                self.current_tab = Tab::ScriptEdit;\n+            }\n+            if ui\n+                .selectable_label(self.current_tab == Tab::Settings, \"设置\")\n+                .clicked()\n+            {\n+                self.current_tab = Tab::Settings;\n+            }\n+        });\n+\n+        // 中央内容面板\n+        egui::CentralPanel::default().show(ctx, |ui| match self.current_tab {\n+            Tab::Capture => self.show_capture_tab(ui),\n+            Tab::Playback => self.show_playback_tab(ui),\n+            Tab::ScriptEdit => self.show_script_edit_tab(ui),\n+            Tab::Settings => self.show_settings_tab(ui),\n+        });\n+    }\n+}\n+\n+impl ActoApp {\n+    // 事件捕获选项卡\n+    fn show_capture_tab(&mut self, ui: &mut egui::Ui) {\n+        ui.heading(\"事件捕获\");\n+        ui.separator();\n+\n+        ui.horizontal(|ui| {\n+            ui.label(\"输出文件路径:\");\n+            ui.text_edit_singleline(&mut self.capture_output_path);\n+        });\n+\n+        ui.horizontal(|ui| {\n+            ui.label(\"捕获时长(秒, 0表示无限制):\");\n+            ui.text_edit_singleline(&mut self.capture_duration);\n+        });\n+\n+        ui.horizontal(|ui| {\n+            if !self.is_capturing {\n+                if ui.button(\"开始捕获\").clicked() {\n+                    self.start_capture();\n+                }\n+            } else {\n+                if ui.button(\"停止捕获\").clicked() {\n+                    self.stop_capture();\n+                }\n+            }\n+        });\n+\n+        ui.add_space(10.0);\n+        ui.label(\"提示: 捕获过程中按ESC键可以停止捕获\");\n+    }\n+\n+    // 脚本回放选项卡\n+    fn show_playback_tab(&mut self, ui: &mut egui::Ui) {\n+        ui.heading(\"脚本回放\");\n+        ui.separator();\n+\n+        ui.horizontal(|ui| {\n+            ui.label(\"脚本文件路径:\");\n+            ui.text_edit_singleline(&mut self.playback_script_path);\n+        });\n+\n+        ui.horizontal(|ui| {\n+            ui.label(\"回放速度(倍数):\");\n+            ui.text_edit_singleline(&mut self.playback_speed);\n+        });\n+\n+        ui.horizontal(|ui| {\n+            if !self.is_playing {\n+                if ui.button(\"开始回放\").clicked() {\n+                    self.start_playback();\n+                }\n+            } else {\n+                // 目前没有实现停止回放的功能\n+                ui.label(\"回放中...\");\n+            }\n+        });\n+\n+        ui.add_space(10.0);\n+        ui.label(\"提示: 回放开始前会有3秒倒计时\");\n+    }\n+\n+    // 脚本编辑选项卡\n+    fn show_script_edit_tab(&mut self, ui: &mut egui::Ui) {\n+        ui.heading(\"脚本编辑\");\n+        ui.separator();\n+\n+        ui.horizontal(|ui| {\n+            ui.label(\"脚本文件路径:\");\n+            ui.text_edit_singleline(&mut self.script_edit_path);\n+            if ui.button(\"加载\").clicked() {\n+                self.load_script();\n+            }\n+            if ui.button(\"保存\").clicked() {\n+                self.save_script();\n+            }\n+        });\n+\n+        ui.horizontal(|ui| {\n+            if ui.button(\"生成可读文本\").clicked() {\n+                self.generate_readable_text();\n+            }\n+            if ui.button(\"调整脚本速度\").clicked() {\n+                self.adjust_script_speed();\n+            }\n+        });\n+\n+        ui.add_space(10.0);\n+\n+        // 脚本内容编辑区\n+        egui::ScrollArea::vertical().show(ui, |ui| {\n+            ui.add(\n+                egui::TextEdit::multiline(&mut self.script_content)\n+                    .desired_width(f32::INFINITY)\n+                    .desired_rows(20),\n+            );\n+        });\n+    }\n+\n+    // 设置选项卡\n+    fn show_settings_tab(&mut self, ui: &mut egui::Ui) {\n+        ui.heading(\"设置\");\n+        ui.separator();\n+        ui.label(\"暂无可配置选项\");\n+    }\n+\n+    // 开始捕获事件\n+    fn start_capture(&mut self) {\n+        if self.is_capturing {\n+            return;\n+        }\n+\n+        let output_path = self.capture_output_path.clone();\n+        let duration_str = self.capture_duration.clone();\n+        let duration = duration_str.parse::<u64>().unwrap_or(0);\n+\n+        let status_message = Arc::new(Mutex::new(self.status_message.clone()));\n+        let status_clone = Arc::clone(&status_message);\n+\n+        self.capture_thread = Some(thread::spawn(move || {\n+            *status_clone.lock().unwrap() = \"正在捕获事件...\".to_string();\n+            match capture_events(PathBuf::from(output_path), duration) {\n+                Ok(_) => *status_clone.lock().unwrap() = \"事件捕获完成\".to_string(),\n+                Err(e) => *status_clone.lock().unwrap() = format!(\"捕获错误: {}\", e),\n+            }\n+        }));\n+\n+        self.is_capturing = true;\n+        self.status_message = \"正在捕获事件...\".to_string();\n+    }\n+\n+    // 停止捕获事件\n+    fn stop_capture(&mut self) {\n+        // 目前没有实现手动停止捕获的功能\n+        // 捕获会在按下ESC键或达到指定时长后自动停止\n+        self.is_capturing = false;\n+        self.status_message = \"捕获已停止\".to_string();\n+    }\n+\n+    // 开始回放脚本\n+    fn start_playback(&mut self) {\n+        if self.is_playing {\n+            return;\n+        }\n+\n+        let script_path = self.playback_script_path.clone();\n+        let speed_str = self.playback_speed.clone();\n+        let speed = speed_str.parse::<f64>().unwrap_or(1.0);\n+\n+        let status_message = Arc::new(Mutex::new(self.status_message.clone()));\n+        let status_clone = Arc::clone(&status_message);\n+\n+        self.playback_thread = Some(thread::spawn(move || {\n+            *status_clone.lock().unwrap() = \"正在回放脚本...\".to_string();\n+            match playback_script(PathBuf::from(script_path), speed) {\n+                Ok(_) => *status_clone.lock().unwrap() = \"脚本回放完成\".to_string(),\n+                Err(e) => *status_clone.lock().unwrap() = format!(\"回放错误: {}\", e),\n+            }\n+        }));\n+\n+        self.is_playing = true;\n+        self.status_message = \"正在回放脚本...\".to_string();\n+    }\n+\n+    // 加载脚本\n+    fn load_script(&mut self) {\n+        if self.script_edit_path.is_empty() {\n+            self.status_message = \"请指定脚本文件路径\".to_string();\n+            return;\n+        }\n+\n+        match script_generator::load_script(&PathBuf::from(&self.script_edit_path)) {\n+            Ok(script) => {\n+                self.script = Some(script.clone());\n+                match serde_json::to_string_pretty(&script) {\n+                    Ok(content) => {\n+                        self.script_content = content;\n+                        self.status_message = \"脚本加载成功\".to_string();\n+                    }\n+                    Err(e) => self.status_message = format!(\"脚本格式化错误: {}\", e),\n+                }\n+            }\n+            Err(e) => self.status_message = format!(\"脚本加载错误: {}\", e),\n+        }\n+    }\n+\n+    // 保存脚本\n+    fn save_script(&mut self) {\n+        if self.script_edit_path.is_empty() {\n+            self.status_message = \"请指定脚本文件路径\".to_string();\n+            return;\n+        }\n+\n+        match serde_json::from_str::<Script>(&self.script_content) {\n+            Ok(script) => {\n+                match script_generator::save_script(&script, &PathBuf::from(&self.script_edit_path))\n+                {\n+                    Ok(_) => self.status_message = \"脚本保存成功\".to_string(),\n+                    Err(e) => self.status_message = format!(\"脚本保存错误: {}\", e),\n+                }\n+            }\n+            Err(e) => self.status_message = format!(\"脚本解析错误: {}\", e),\n+        }\n+    }\n+\n+    // 生成可读文本\n+    fn generate_readable_text(&mut self) {\n+        if let Some(script) = &self.script {\n+            self.script_content = script_generator::script_to_readable_text(script);\n+            self.status_message = \"已生成可读文本\".to_string();\n+        } else {\n+            self.status_message = \"请先加载脚本\".to_string();\n+        }\n+    }\n+\n+    // 调整脚本速度\n+    fn adjust_script_speed(&mut self) {\n+        if let Some(script) = &self.script {\n+            let speed_str = self.playback_speed.clone();\n+            let speed = speed_str.parse::<f64>().unwrap_or(1.0);\n+\n+            let adjusted_script = script_generator::adjust_script_speed(script, speed);\n+            self.script = Some(adjusted_script.clone());\n+\n+            match serde_json::to_string_pretty(&adjusted_script) {\n+                Ok(content) => {\n+                    self.script_content = content;\n+                    self.status_message = format!(\"脚本速度已调整为{}倍\", speed);\n+                }\n+                Err(e) => self.status_message = format!(\"脚本格式化错误: {}\", e),\n+            }\n+        } else {\n+            self.status_message = \"请先加载脚本\".to_string();\n+        }\n+    }\n+}\n+\n+pub fn run_gui() -> Result<(), eframe::Error> {\n+    let options = eframe::NativeOptions {\n+        initial_window_size: Some(egui::vec2(800.0, 600.0)),\n+        ..Default::default()\n+    };\n+    eframe::run_native(\n+        \"Acto - 桌面自动化工具\",\n+        options,\n+        Box::new(|_cc| Box::new(ActoApp::default())),\n+    )\n+}\n+\n+fn load_fonts(ctx: &egui::Context) {\n+    let mut fonts = egui::FontDefinitions::default();\n+    fonts.font_data.insert(\"my_font\".to_owned(),\n+    egui::FontData::from_static(include_bytes!(\"re\")));\n+    fonts.families.get_mut(&egui::FontFamily::Proportional).unwrap()\n+        .insert(0, \"my_font\".to_owned());\n+    fonts.families.get_mut(&egui::FontFamily::Monospace).unwrap()\n+        .push(\"my_font\".to_owned());\n+    ctx.set_fonts(fonts);\n+}\n"}, {"date": 1747969566219, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -370,9 +370,9 @@\n \n fn load_fonts(ctx: &egui::Context) {\n     let mut fonts = egui::FontDefinitions::default();\n     fonts.font_data.insert(\"my_font\".to_owned(),\n-    egui::FontData::from_static(include_bytes!(\"res\")));\n+    egui::FontData::from_static(include_bytes!(\"resources/\")));\n     fonts.families.get_mut(&egui::FontFamily::Proportional).unwrap()\n         .insert(0, \"my_font\".to_owned());\n     fonts.families.get_mut(&egui::FontFamily::Monospace).unwrap()\n         .push(\"my_font\".to_owned());\n"}, {"date": 1747969587300, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -370,9 +370,9 @@\n \n fn load_fonts(ctx: &egui::Context) {\n     let mut fonts = egui::FontDefinitions::default();\n     fonts.font_data.insert(\"my_font\".to_owned(),\n-    egui::FontData::from_static(include_bytes!(\"resources/\")));\n+    egui::FontData::from_static(include_bytes!(\"resources/fonts/\")));\n     fonts.families.get_mut(&egui::FontFamily::Proportional).unwrap()\n         .insert(0, \"my_font\".to_owned());\n     fonts.families.get_mut(&egui::FontFamily::Monospace).unwrap()\n         .push(\"my_font\".to_owned());\n"}], "date": 1747967189236, "name": "Commit-0", "content": "use crate::event_capture::{self, <PERSON><PERSON><PERSON>};\nuse crate::playback_control;\nuse crate::script_generator;\nuse eframe::{egui, epi};\nuse egui::{Color32, RichText, Ui};\nuse std::path::PathBuf;\nuse std::sync::{Arc, Mutex};\nuse std::thread;\n\npub struct ActoApp {\n    current_tab: Tab,\n    capture_state: CaptureState,\n    playback_state: PlaybackState,\n    script_state: ScriptState,\n    status_message: String,\n}\n\n#[derive(PartialEq)]\nenum Tab {\n    Capture,\n    Playback,\n    ScriptEdit,\n    Settings,\n}\n\nstruct CaptureState {\n    is_capturing: bool,\n    output_path: String,\n    duration_secs: String,\n    capture_thread: Option<std::thread::Jo<PERSON><PERSON><PERSON><PERSON><()>>,\n    should_stop: Arc<Mutex<bool>>,\n}\n\nstruct PlaybackState {\n    script_path: String,\n    speed_factor: f64,\n    is_playing: bool,\n    playback_thread: Option<std::thread::<PERSON><PERSON><PERSON><PERSON><PERSON><()>>,\n}\n\nstruct ScriptState {\n    current_script: Option<Script>,\n    script_path: String,\n    script_text: String,\n    is_modified: bool,\n}\n\nimpl Default for ActoApp {\n    fn default() -> Self {\n        Self {\n            current_tab: Tab::Capture,\n            capture_state: CaptureState {\n                is_capturing: false,\n                output_path: \"output.json\".to_string(),\n                duration_secs: \"\".to_string(),\n                capture_thread: None,\n                should_stop: Arc::new(Mutex::new(false)),\n            },\n            playback_state: PlaybackState {\n                script_path: \"\".to_string(),\n                speed_factor: 1.0,\n                is_playing: false,\n                playback_thread: None,\n            },\n            script_state: ScriptState {\n                current_script: None,\n                script_path: \"\".to_string(),\n                script_text: \"\".to_string(),\n                is_modified: false,\n            },\n            status_message: \"就绪\".to_string(),\n        }\n    }\n}\n\nimpl epi::App for ActoApp {\n    fn name(&self) -> &str {\n        \"Acto - 桌面自动化工具\"\n    }\n\n    fn update(&mut self, ctx: &egui::Context, _frame: &epi::Frame) {\n        egui::TopBottomPanel::top(\"top_panel\").show(ctx, |ui| {\n            egui::menu::bar(ui, |ui| {\n                ui.menu_button(\"文件\", |ui| {\n                    if ui.button(\"打开脚本\").clicked() {\n                        // 这里应该打开文件对话框，但egui目前不直接支持\n                        // 可以使用native_dialog或rfd库实现\n                        ui.close_menu();\n                    }\n                    if ui.button(\"保存脚本\").clicked() {\n                        // 保存当前脚本\n                        ui.close_menu();\n                    }\n                    if ui.button(\"退出\").clicked() {\n                        std::process::exit(0);\n                    }\n                });\n                ui.menu_button(\"帮助\", |ui| {\n                    if ui.button(\"关于\").clicked() {\n                        self.status_message = \"Acto - 桌面自动化工具 v0.1.0\".to_string();\n                        ui.close_menu();\n                    }\n                });\n            });\n        });\n\n        egui::TopBottomPanel::bottom(\"bottom_panel\").show(ctx, |ui| {\n            ui.horizontal(|ui| {\n                ui.label(\"状态: \");\n                ui.label(&self.status_message);\n            });\n        });\n\n        egui::SidePanel::left(\"side_panel\").show(ctx, |ui| {\n            ui.heading(\"功能导航\");\n            ui.separator();\n\n            if ui\n                .selectable_label(self.current_tab == Tab::Capture, \"事件捕获\")\n                .clicked()\n            {\n                self.current_tab = Tab::Capture;\n            }\n            if ui\n                .selectable_label(self.current_tab == Tab::Playback, \"脚本回放\")\n                .clicked()\n            {\n                self.current_tab = Tab::Playback;\n            }\n            if ui\n                .selectable_label(self.current_tab == Tab::ScriptEdit, \"脚本编辑\")\n                .clicked()\n            {\n                self.current_tab = Tab::ScriptEdit;\n            }\n            if ui\n                .selectable_label(self.current_tab == Tab::Settings, \"设置\")\n                .clicked()\n            {\n                self.current_tab = Tab::Settings;\n            }\n        });\n\n        egui::CentralPanel::default().show(ctx, |ui| match self.current_tab {\n            Tab::Capture => self.render_capture_tab(ui),\n            Tab::Playback => self.render_playback_tab(ui),\n            Tab::ScriptEdit => self.render_script_edit_tab(ui),\n            Tab::Settings => self.render_settings_tab(ui),\n        });\n    }\n}\n\nimpl ActoApp {\n    fn render_capture_tab(&mut self, ui: &mut Ui) {\n        ui.heading(\"事件捕获\");\n        ui.separator();\n\n        ui.horizontal(|ui| {\n            ui.label(\"输出文件路径: \");\n            ui.text_edit_singleline(&mut self.capture_state.output_path);\n        });\n\n        ui.horizontal(|ui| {\n            ui.label(\"捕获时长(秒): \");\n            ui.text_edit_singleline(&mut self.capture_state.duration_secs);\n            ui.label(\"(留空表示无限制，按ESC停止)\");\n        });\n\n        ui.add_space(10.0);\n\n        if !self.capture_state.is_capturing {\n            if ui.button(\"开始捕获\").clicked() {\n                self.start_capture();\n            }\n        } else {\n            if ui.button(\"停止捕获\").clicked() {\n                self.stop_capture();\n            }\n        }\n\n        ui.add_space(20.0);\n        ui.label(RichText::new(\"使用说明:\").color(Color32::YELLOW));\n        ui.label(\"1. 设置输出文件路径和可选的捕获时长\");\n        ui.label(\"2. 点击'开始捕获'按钮\");\n        ui.label(\"3. 执行您想要自动化的操作\");\n        ui.label(\"4. 按ESC键或点击'停止捕获'按钮结束捕获\");\n    }\n\n    fn render_playback_tab(&mut self, ui: &mut Ui) {\n        ui.heading(\"脚本回放\");\n        ui.separator();\n\n        ui.horizontal(|ui| {\n            ui.label(\"脚本文件路径: \");\n            ui.text_edit_singleline(&mut self.playback_state.script_path);\n            if ui.button(\"浏览...\").clicked() {\n                // 这里应该打开文件对话框\n            }\n        });\n\n        ui.horizontal(|ui| {\n            ui.label(\"回放速度: \");\n            ui.add(\n                egui::Slider::new(&mut self.playback_state.speed_factor, 0.1..=5.0).text(\"倍速\"),\n            );\n        });\n\n        ui.add_space(10.0);\n\n        if !self.playback_state.is_playing {\n            if ui.button(\"开始回放\").clicked() {\n                self.start_playback();\n            }\n        } else {\n            if ui.button(\"停止回放\").clicked() {\n                // 目前没有实现停止回放的功能\n                self.status_message = \"回放停止功能尚未实现\".to_string();\n            }\n        }\n\n        ui.add_space(20.0);\n        ui.label(RichText::new(\"使用说明:\").color(Color32::YELLOW));\n        ui.label(\"1. 选择要回放的脚本文件\");\n        ui.label(\"2. 调整回放速度（1.0为正常速度）\");\n        ui.label(\"3. 点击'开始回放'按钮\");\n        ui.label(\"4. 回放将在3秒倒计时后开始\");\n    }\n\n    fn render_script_edit_tab(&mut self, ui: &mut Ui) {\n        ui.heading(\"脚本编辑\");\n        ui.separator();\n\n        ui.horizontal(|ui| {\n            ui.label(\"脚本文件: \");\n            ui.text_edit_singleline(&mut self.script_state.script_path);\n\n            if ui.button(\"加载\").clicked() {\n                self.load_script();\n            }\n\n            if ui.button(\"保存\").clicked() {\n                self.save_script();\n            }\n        });\n\n        ui.add_space(10.0);\n\n        if let Some(script) = &self.script_state.current_script {\n            ui.label(format!(\"事件数量: {}\", script.events.len()));\n            ui.label(format!(\"总时长: {}毫秒\", script.duration));\n\n            ui.add_space(10.0);\n\n            if ui.button(\"生成可读文本\").clicked() {\n                self.script_state.script_text = script_generator::script_to_readable_text(script);\n            }\n\n            if ui.button(\"调整速度\").clicked() {\n                if let Some(script) = &self.script_state.current_script {\n                    let adjusted = script_generator::adjust_script_speed(\n                        script,\n                        self.playback_state.speed_factor,\n                    );\n                    self.script_state.current_script = Some(adjusted);\n                    self.script_state.is_modified = true;\n                    self.status_message =\n                        format!(\"脚本速度已调整为{}倍\", self.playback_state.speed_factor);\n                }\n            }\n        } else {\n            ui.label(\"未加载脚本\");\n        }\n\n        ui.add_space(10.0);\n\n        // 显示脚本文本\n        egui::ScrollArea::vertical().show(ui, |ui| {\n            ui.add(\n                egui::TextEdit::multiline(&mut self.script_state.script_text)\n                    .desired_width(f32::INFINITY)\n                    .desired_rows(20)\n                    .code_editor()\n                    .lock_focus(true)\n                    .hint_text(\"脚本内容将显示在这里\"),\n            );\n        });\n    }\n\n    fn render_settings_tab(&mut self, ui: &mut Ui) {\n        ui.heading(\"设置\");\n        ui.separator();\n\n        ui.label(\"此版本暂无可配置选项\");\n    }\n\n    fn start_capture(&mut self) {\n        if self.capture_state.is_capturing {\n            return;\n        }\n\n        let output_path = PathBuf::from(&self.capture_state.output_path);\n        let duration = if self.capture_state.duration_secs.is_empty() {\n            None\n        } else {\n            match self.capture_state.duration_secs.parse::<u64>() {\n                Ok(secs) => Some(secs),\n                Err(_) => {\n                    self.status_message = \"无效的捕获时长，请输入一个正整数\".to_string();\n                    return;\n                }\n            }\n        };\n\n        // 重置停止标志\n        let should_stop = Arc::new(Mutex::new(false));\n        self.capture_state.should_stop = should_stop.clone();\n\n        // 创建线程进行捕获\n        let output_path_clone = output_path.clone();\n        let handle = thread::spawn(move || {\n            event_capture::start_capture(output_path_clone, duration);\n        });\n\n        self.capture_state.capture_thread = Some(handle);\n        self.capture_state.is_capturing = true;\n        self.status_message = \"正在捕获事件...\".to_string();\n    }\n\n    fn stop_capture(&mut self) {\n        if !self.capture_state.is_capturing {\n            return;\n        }\n\n        // 设置停止标志\n        if let Ok(mut stop) = self.capture_state.should_stop.lock() {\n            *stop = true;\n        }\n\n        // 等待捕获线程结束\n        if let Some(handle) = self.capture_state.capture_thread.take() {\n            // 这里不等待线程结束，因为可能会阻塞UI\n            // 实际应用中应该使用更复杂的机制\n        }\n\n        self.capture_state.is_capturing = false;\n        self.status_message = \"捕获已停止\".to_string();\n    }\n\n    fn start_playback(&mut self) {\n        if self.playback_state.is_playing {\n            return;\n        }\n\n        let script_path = PathBuf::from(&self.playback_state.script_path);\n        if !script_path.exists() {\n            self.status_message = \"脚本文件不存在\".to_string();\n            return;\n        }\n\n        let speed_factor = self.playback_state.speed_factor;\n        let script_path_clone = script_path.clone();\n\n        // 创建线程进行回放\n        let handle = thread::spawn(move || {\n            playback_control::start_playback(&script_path_clone, speed_factor);\n        });\n\n        self.playback_state.playback_thread = Some(handle);\n        self.playback_state.is_playing = true;\n        self.status_message = \"正在回放脚本...\".to_string();\n\n        // 设置一个定时器，在一段时间后检查回放是否完成\n        // 这里简化处理，实际应用中应该使用更复杂的机制\n        let playback_time = 5000; // 假设回放需要5秒\n        thread::spawn(move || {\n            thread::sleep(std::time::Duration::from_millis(playback_time));\n            // 这里应该通知主线程回放完成\n        });\n    }\n\n    fn load_script(&mut self) {\n        let path = PathBuf::from(&self.script_state.script_path);\n        match script_generator::load_script(&path) {\n            Ok(script) => {\n                self.script_state.current_script = Some(script);\n                self.script_state.script_text =\n                    \"脚本加载成功，点击'生成可读文本'查看内容\".to_string();\n                self.script_state.is_modified = false;\n                self.status_message = \"脚本加载成功\".to_string();\n            }\n            Err(e) => {\n                self.status_message = format!(\"加载脚本失败: {}\", e);\n            }\n        }\n    }\n\n    fn save_script(&mut self) {\n        if let Some(script) = &self.script_state.current_script {\n            let path = PathBuf::from(&self.script_state.script_path);\n            match script_generator::save_script(script, &path) {\n                Ok(_) => {\n                    self.script_state.is_modified = false;\n                    self.status_message = \"脚本保存成功\".to_string();\n                }\n                Err(e) => {\n                    self.status_message = format!(\"保存脚本失败: {}\", e);\n                }\n            }\n        } else {\n            self.status_message = \"没有脚本可保存\".to_string();\n        }\n    }\n}\n\npub fn start_gui() {\n    let app = ActoApp::default();\n    let native_options = eframe::NativeOptions::default();\n    eframe::run_native(Box::new(app), native_options);\n}\n"}]}