{"sourceFile": "src/main.rs", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1747967032411, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747967314284, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -14,54 +14,48 @@\n }\n \n #[derive(Subcommand)]\n enum Commands {\n-    /// 捕获用户的鼠标和键盘事件\n+    /// 捕获鼠标键盘事件并保存到文件\n     Capture {\n-        /// 输出脚本的文件路径\n+        /// 输出文件路径\n         #[arg(short, long)]\n-        output: Option<PathBuf>,\n+        output: PathBuf,\n \n-        /// 捕获时长（秒），默认无限制直到按Esc键\n-        #[arg(short, long)]\n-        duration: Option<u64>,\n+        /// 捕获时长（秒），0表示无限制，按ESC停止\n+        #[arg(short, long, default_value_t = 0)]\n+        duration: u64,\n     },\n-    /// 回放之前录制的脚本\n+    /// 回放脚本文件\n     Playback {\n         /// 脚本文件路径\n         #[arg(short, long)]\n         script: PathBuf,\n \n-        /// 回放速度倍率，默认为1.0\n-        #[arg(short, long, default_value = \"1.0\")]\n+        /// 回放速度（倍数）\n+        #[arg(short, long, default_value_t = 1.0)]\n         speed: f64,\n     },\n     /// 启动图形界面\n     Gui,\n }\n \n-fn main() {\n+fn main() -> Result<(), Box<dyn std::error::Error>> {\n     let cli = Cli::parse();\n \n     match &cli.command {\n         Some(Commands::Capture { output, duration }) => {\n-            println!(\"捕获模式启动\");\n-            let output_path = output\n-                .clone()\n-                .unwrap_or_else(|| PathBuf::from(\"output.json\"));\n-            event_capture::start_capture(output_path, *duration);\n+            println!(\"捕获事件到文件: {:?}, 时长: {}秒\", output, duration);\n+            event_capture::capture_events(output.clone(), *duration)?;\n         }\n         Some(Commands::Playback { script, speed }) => {\n-            println!(\"回放模式启动\");\n-            playback_control::start_playback(script, *speed);\n+            println!(\"回放脚本: {:?}, 速度: {}倍\", script, speed);\n+            playback_control::playback_script(script.clone(), *speed)?;\n         }\n-        Some(Commands::Gui) => {\n-            println!(\"GUI模式启动\");\n-            gui::start_gui();\n+        Some(Commands::Gui) | None => {\n+            println!(\"启动图形界面\");\n+            gui::run_gui()?;\n         }\n-        None => {\n-            // 默认启动GUI\n-            println!(\"默认启动GUI模式\");\n-            gui::start_gui();\n-        }\n     }\n+\n+    Ok(())\n }\n"}, {"date": 1747977087387, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,83 +1,83 @@\n use makepad_widgets::*;\n \n-live_design!{\n+live_design! {\n     import makepad_widgets::base::*;\n     import makepad_widgets::theme_desktop_dark::*;\n-    \n+\n     App = {{App}} {\n         ui: <Window>{\n             show_bg: true\n             width: Fill,\n             height: Fill\n-            \n+\n             draw_bg: {\n                 fn pixel(self) -> vec4 {\n                     return mix(#2, #3, self.pos.y);\n                 }\n             }\n-            \n+\n             body = <ScrollXYView>{\n                 flow: Down,\n                 spacing: 20,\n                 align: {x: 0.5, y: 0.5},\n-                \n+\n                 <View> {\n                     width: 800, height: 600,\n                     flow: Down,\n                     spacing: 15,\n                     padding: 20,\n-                    \n+\n                     draw_bg: {\n                         color: #fff\n                         radius: 10.0\n                     }\n-                    \n+\n                     <Label> {\n                         draw_text: {\n                             text_style: <THEME_FONT_BOLD>{font_size: 24},\n                             color: #000\n                         }\n                         text: \"桌面自动化工具 - Acto\"\n                     }\n-                    \n+\n                     <View> {\n                         flow: Right,\n                         spacing: 10,\n-                        \n+\n                         record_btn = <Button> {\n                             text: \"开始录制\"\n                             draw_bg: {\n                                 color: #4CAF50\n                             }\n                         }\n-                        \n+\n                         stop_btn = <Button> {\n                             text: \"停止录制\"\n                             draw_bg: {\n                                 color: #f44336\n                             }\n                         }\n-                        \n+\n                         play_btn = <Button> {\n                             text: \"播放脚本\"\n                             draw_bg: {\n                                 color: #2196F3\n                             }\n                         }\n                     }\n-                    \n+\n                     <View> {\n                         flow: Down,\n                         spacing: 10,\n-                        \n+\n                         <Label> {\n                             text: \"脚本内容:\"\n                             draw_text: {\n                                 color: #000\n                             }\n                         }\n-                        \n+\n                         script_view = <TextInput> {\n                             width: Fill,\n                             height: 300,\n                             text: \"\"\n@@ -85,28 +85,28 @@\n                                 color: #f5f5f5\n                             }\n                         }\n                     }\n-                    \n+\n                     <View> {\n                         flow: Right,\n                         spacing: 10,\n-                        \n+\n                         save_btn = <Button> {\n                             text: \"保存脚本\"\n                             draw_bg: {\n                                 color: #FF9800\n                             }\n                         }\n-                        \n+\n                         load_btn = <Button> {\n                             text: \"加载脚本\"\n                             draw_bg: {\n                                 color: #9C27B0\n                             }\n                         }\n                     }\n-                    \n+\n                     status_label = <Label> {\n                         text: \"状态: 就绪\"\n                         draw_text: {\n                             color: #666\n@@ -121,48 +121,58 @@\n app_main!(App);\n \n #[derive(Live, LiveHook)]\n pub struct App {\n-    #[live] ui: WidgetRef,\n-    #[rust] automation_engine: AutomationEngine,\n+    #[live]\n+    ui: WidgetRef,\n+    #[rust]\n+    automation_engine: AutomationEngine,\n }\n \n impl LiveRegister for App {\n     fn live_register(cx: &mut Cx) {\n         crate::makepad_widgets::live_design(cx);\n     }\n }\n \n-impl MatchEvent for App{\n-    fn handle_actions(&mut self, cx: &mut Cx, actions:&Actions){\n+impl MatchEvent for App {\n+    fn handle_actions(&mut self, cx: &mut Cx, actions: &Actions) {\n         if self.ui.button(id!(record_btn)).clicked(&actions) {\n             self.automation_engine.start_recording();\n-            self.ui.label(id!(status_label)).set_text(\"状态: 正在录制...\");\n+            self.ui\n+                .label(id!(status_label))\n+                .set_text(\"状态: 正在录制...\");\n         }\n-        \n+\n         if self.ui.button(id!(stop_btn)).clicked(&actions) {\n             self.automation_engine.stop_recording();\n             let script = self.automation_engine.get_script();\n             self.ui.text_input(id!(script_view)).set_text(&script);\n             self.ui.label(id!(status_label)).set_text(\"状态: 录制完成\");\n         }\n-        \n+\n         if self.ui.button(id!(play_btn)).clicked(&actions) {\n             let script = self.ui.text_input(id!(script_view)).text();\n             self.automation_engine.execute_script(&script);\n-            self.ui.label(id!(status_label)).set_text(\"状态: 正在执行脚本...\");\n+            self.ui\n+                .label(id!(status_label))\n+                .set_text(\"状态: 正在执行脚本...\");\n         }\n-        \n+\n         if self.ui.button(id!(save_btn)).clicked(&actions) {\n             let script = self.ui.text_input(id!(script_view)).text();\n             self.automation_engine.save_script(&script);\n-            self.ui.label(id!(status_label)).set_text(\"状态: 脚本已保存\");\n+            self.ui\n+                .label(id!(status_label))\n+                .set_text(\"状态: 脚本已保存\");\n         }\n-        \n+\n         if self.ui.button(id!(load_btn)).clicked(&actions) {\n             if let Some(script) = self.automation_engine.load_script() {\n                 self.ui.text_input(id!(script_view)).set_text(&script);\n-                self.ui.label(id!(status_label)).set_text(\"状态: 脚本已加载\");\n+                self.ui\n+                    .label(id!(status_label))\n+                    .set_text(\"状态: 脚本已加载\");\n             }\n         }\n     }\n }\n@@ -174,5 +184,5 @@\n     }\n }\n \n mod automation_engine;\n-use automation_engine::AutomationEngine;\n\\ No newline at end of file\n+use automation_engine::AutomationEngine;\n"}, {"date": 1748224919998, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,4 @@\n fn main() {\n-    env_logger::init();\n+    env_logger::\n     acto::app_main()\n }\n"}], "date": 1747967032411, "name": "Commit-0", "content": "mod event_capture;\nmod gui;\nmod playback_control;\nmod script_generator;\n\nuse clap::{Parser, Subcommand};\nuse std::path::PathBuf;\n\n#[derive(Parser)]\n#[command(author, version, about, long_about = None)]\nstruct Cli {\n    #[command(subcommand)]\n    command: Option<Commands>,\n}\n\n#[derive(Subcommand)]\nenum Commands {\n    /// 捕获用户的鼠标和键盘事件\n    Capture {\n        /// 输出脚本的文件路径\n        #[arg(short, long)]\n        output: Option<PathBuf>,\n\n        /// 捕获时长（秒），默认无限制直到按Esc键\n        #[arg(short, long)]\n        duration: Option<u64>,\n    },\n    /// 回放之前录制的脚本\n    Playback {\n        /// 脚本文件路径\n        #[arg(short, long)]\n        script: PathBuf,\n\n        /// 回放速度倍率，默认为1.0\n        #[arg(short, long, default_value = \"1.0\")]\n        speed: f64,\n    },\n    /// 启动图形界面\n    Gui,\n}\n\nfn main() {\n    let cli = Cli::parse();\n\n    match &cli.command {\n        Some(Commands::Capture { output, duration }) => {\n            println!(\"捕获模式启动\");\n            let output_path = output\n                .clone()\n                .unwrap_or_else(|| PathBuf::from(\"output.json\"));\n            event_capture::start_capture(output_path, *duration);\n        }\n        Some(Commands::Playback { script, speed }) => {\n            println!(\"回放模式启动\");\n            playback_control::start_playback(script, *speed);\n        }\n        Some(Commands::Gui) => {\n            println!(\"GUI模式启动\");\n            gui::start_gui();\n        }\n        None => {\n            // 默认启动GUI\n            println!(\"默认启动GUI模式\");\n            gui::start_gui();\n        }\n    }\n}\n"}]}