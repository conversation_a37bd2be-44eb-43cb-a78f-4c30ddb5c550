{"sourceFile": "src/script_generator.rs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1747967091586, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747974948749, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -159,5 +159,5 @@\n         }\n \n         result\n     }\n-}\n+}\n\\ No newline at end of file\n"}], "date": 1747967091586, "name": "Commit-0", "content": "use crate::event_capture::{RecordedE<PERSON>, <PERSON>ript};\nuse std::fs::File;\nuse std::io::{Read, Write};\nuse std::path::PathBuf;\n\n/// 从文件加载脚本\npub fn load_script(path: &PathBuf) -> Result<Script, String> {\n    let mut file = match File::open(path) {\n        Ok(file) => file,\n        Err(e) => return Err(format!(\"无法打开文件: {}\", e)),\n    };\n\n    let mut contents = String::new();\n    if let Err(e) = file.read_to_string(&mut contents) {\n        return Err(format!(\"无法读取文件内容: {}\", e));\n    }\n\n    match serde_json::from_str(&contents) {\n        Ok(script) => Ok(script),\n        Err(e) => Err(format!(\"无法解析脚本文件: {}\", e)),\n    }\n}\n\n/// 保存脚本到文件\npub fn save_script(script: &Script, path: &PathBuf) -> Result<(), String> {\n    let json = match serde_json::to_string_pretty(script) {\n        Ok(json) => json,\n        Err(e) => return Err(format!(\"无法序列化脚本: {}\", e)),\n    };\n\n    let mut file = match File::create(path) {\n        Ok(file) => file,\n        Err(e) => return Err(format!(\"无法创建文件: {}\", e)),\n    };\n\n    if let Err(e) = file.write_all(json.as_bytes()) {\n        return Err(format!(\"无法写入文件: {}\", e));\n    }\n\n    Ok(())\n}\n\n/// 合并两个脚本\npub fn merge_scripts(script1: &Script, script2: &Script) -> Script {\n    let mut merged_events = script1.events.clone();\n\n    // 如果第二个脚本的事件不为空，调整时间戳并添加到合并后的事件列表\n    if !script2.events.is_empty() {\n        let last_timestamp = script1.events.last().map_or(0, |e| e.timestamp);\n        let first_timestamp = script2.events.first().map_or(0, |e| e.timestamp);\n\n        // 计算时间戳偏移量，确保事件连续\n        let offset = last_timestamp + 500; // 添加500毫秒的间隔\n\n        // 添加调整后的事件\n        for event in &script2.events {\n            let mut adjusted_event = event.clone();\n            adjusted_event.timestamp = offset + (event.timestamp - first_timestamp);\n            merged_events.push(adjusted_event);\n        }\n    }\n\n    // 计算新的总时长\n    let duration = if merged_events.is_empty() {\n        0\n    } else {\n        merged_events.last().unwrap().timestamp as u64\n    };\n\n    Script {\n        events: merged_events,\n        created_at: chrono::Local::now().to_rfc3339(),\n        duration,\n    }\n}\n\n/// 修剪脚本，删除指定时间范围内的事件\npub fn trim_script(script: &Script, start_ms: u128, end_ms: u128) -> Script {\n    let filtered_events: Vec<RecordedEvent> = script\n        .events\n        .iter()\n        .filter(|event| event.timestamp < start_ms || event.timestamp > end_ms)\n        .cloned()\n        .collect();\n\n    // 重新计算时间戳，使事件连续\n    let mut adjusted_events = Vec::new();\n    let mut current_timestamp = 0;\n\n    for (i, event) in filtered_events.iter().enumerate() {\n        let mut adjusted_event = event.clone();\n\n        if i > 0 {\n            let prev_timestamp = filtered_events[i - 1].timestamp;\n            let delta = if event.timestamp > prev_timestamp {\n                event.timestamp - prev_timestamp\n            } else {\n                100 // 如果时间戳不连续，添加100毫秒的间隔\n            };\n\n            current_timestamp += delta;\n        }\n\n        adjusted_event.timestamp = current_timestamp;\n        adjusted_events.push(adjusted_event);\n    }\n\n    // 计算新的总时长\n    let duration = if adjusted_events.is_empty() {\n        0\n    } else {\n        adjusted_events.last().unwrap().timestamp as u64\n    };\n\n    Script {\n        events: adjusted_events,\n        created_at: chrono::Local::now().to_rfc3339(),\n        duration,\n    }\n}\n\n/// 调整脚本速度\npub fn adjust_script_speed(script: &Script, speed_factor: f64) -> Script {\n    if speed_factor <= 0.0 {\n        return script.clone();\n    }\n\n    let mut adjusted_events = Vec::new();\n    let mut prev_timestamp = 0;\n\n    for (i, event) in script.events.iter().enumerate() {\n        let mut adjusted_event = event.clone();\n\n        if i == 0 {\n            adjusted_event.timestamp = 0;\n        } else {\n            let original_delta = event.timestamp - script.events[i - 1].timestamp;\n            let adjusted_delta = (original_delta as f64 / speed_factor) as u128;\n            adjusted_event.timestamp = prev_timestamp + adjusted_delta;\n        }\n\n        prev_timestamp = adjusted_event.timestamp;\n        adjusted_events.push(adjusted_event);\n    }\n\n    // 计算新的总时长\n    let duration = if adjusted_events.is_empty() {\n        0\n    } else {\n        adjusted_events.last().unwrap().timestamp as u64\n    };\n\n    Script {\n        events: adjusted_events,\n        created_at: chrono::Local::now().to_rfc3339(),\n        duration,\n    }\n}\n\n/// 将脚本转换为人类可读的文本格式\npub fn script_to_readable_text(script: &Script) -> String {\n    let mut text = String::new();\n    text.push_str(&format!(\"创建时间: {}\\n\", script.created_at));\n    text.push_str(&format!(\"总时长: {}毫秒\\n\", script.duration));\n    text.push_str(&format!(\"事件数量: {}\\n\\n\", script.events.len()));\n\n    text.push_str(\"时间戳(ms) | 事件类型 | 动作 | 详情\\n\");\n    text.push_str(\"------------|----------|------|------\\n\");\n\n    for event in &script.events {\n        let detail = match (event.x, event.y) {\n            (Some(x), Some(y)) => format!(\"位置: ({:.1}, {:.1})\", x, y),\n            _ => event.detail.clone(),\n        };\n\n        text.push_str(&format!(\n            \"{:10} | {:8} | {:6} | {}\\n\",\n            event.timestamp, event.event_type, event.action, detail\n        ));\n    }\n\n    text\n}\n"}]}