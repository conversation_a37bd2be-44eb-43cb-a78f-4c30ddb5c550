{"sourceFile": "src/automation_engine.rs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1747977261216, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1747977261216, "name": "Commit-0", "content": "use enigo::{Enigo, KeyboardControllable, MouseControllable};\nuse rdev::{Button, Event, EventType, Key, listen};\nuse serde::{Deserialize, Serialize};\nuse std::fs;\nuse std::path::Path;\nuse std::sync::{<PERSON>, Mutex};\nuse std::thread;\nuse std::time::{Duration, Instant};\n\n#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]\npub enum AutomationAction {\n    MouseMove {\n        x: i32,\n        y: i32,\n        delay: u64,\n    },\n    MouseClick {\n        button: MouseButton,\n        x: i32,\n        y: i32,\n        delay: u64,\n    },\n    KeyPress {\n        key: String,\n        delay: u64,\n    },\n    KeyRelease {\n        key: String,\n        delay: u64,\n    },\n    Wait {\n        duration: u64,\n    },\n}\n\n#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]\npub enum MouseButton {\n    Left,\n    Right,\n    Middle,\n}\n\n#[derive(Debug, Clone)]\npub struct AutomationScript {\n    pub actions: Vec<AutomationAction>,\n    pub name: String,\n    pub created_at: String,\n}\n\npub struct AutomationEngine {\n    is_recording: Arc<Mutex<bool>>,\n    recorded_actions: Arc<Mutex<Vec<AutomationAction>>>,\n    last_event_time: Arc<Mutex<Option<Instant>>>,\n    enigo: Enigo,\n}\n\nimpl Default for AutomationEngine {\n    fn default() -> Self {\n        Self::new()\n    }\n}\n\nimpl AutomationEngine {\n    pub fn new() -> Self {\n        Self {\n            is_recording: Arc::new(Mutex::new(false)),\n            recorded_actions: Arc::new(Mutex::new(Vec::new())),\n            last_event_time: Arc::new(Mutex::new(None)),\n            enigo: Enigo::new(),\n        }\n    }\n\n    pub fn start_recording(&mut self) {\n        let mut is_recording = self.is_recording.lock().unwrap();\n        *is_recording = true;\n\n        let mut recorded_actions = self.recorded_actions.lock().unwrap();\n        recorded_actions.clear();\n\n        let mut last_event_time = self.last_event_time.lock().unwrap();\n        *last_event_time = Some(Instant::now());\n\n        drop(is_recording);\n        drop(recorded_actions);\n        drop(last_event_time);\n\n        // 启动事件监听线程\n        let is_recording_clone = Arc::clone(&self.is_recording);\n        let recorded_actions_clone = Arc::clone(&self.recorded_actions);\n        let last_event_time_clone = Arc::clone(&self.last_event_time);\n\n        thread::spawn(move || {\n            let callback = move |event: Event| {\n                let is_recording = is_recording_clone.lock().unwrap();\n                if !*is_recording {\n                    return;\n                }\n                drop(is_recording);\n\n                let now = Instant::now();\n                let mut last_time = last_event_time_clone.lock().unwrap();\n                let delay = if let Some(last) = *last_time {\n                    now.duration_since(last).as_millis() as u64\n                } else {\n                    0\n                };\n                *last_time = Some(now);\n                drop(last_time);\n\n                let mut actions = recorded_actions_clone.lock().unwrap();\n\n                match event.event_type {\n                    EventType::MouseMove { x, y } => {\n                        actions.push(AutomationAction::MouseMove {\n                            x: x as i32,\n                            y: y as i32,\n                            delay,\n                        });\n                    }\n                    EventType::ButtonPress(button) => {\n                        let mouse_button = match button {\n                            Button::Left => MouseButton::Left,\n                            Button::Right => MouseButton::Right,\n                            Button::Middle => MouseButton::Middle,\n                            _ => return,\n                        };\n\n                        // 获取当前鼠标位置（简化处理）\n                        actions.push(AutomationAction::MouseClick {\n                            button: mouse_button,\n                            x: 0, // 实际应用中需要获取真实坐标\n                            y: 0,\n                            delay,\n                        });\n                    }\n                    EventType::KeyPress(key) => {\n                        let key_str = format!(\"{:?}\", key);\n                        actions.push(AutomationAction::KeyPress {\n                            key: key_str,\n                            delay,\n                        });\n                    }\n                    EventType::KeyRelease(key) => {\n                        let key_str = format!(\"{:?}\", key);\n                        actions.push(AutomationAction::KeyRelease {\n                            key: key_str,\n                            delay,\n                        });\n                    }\n                    _ => {}\n                }\n            };\n\n            if let Err(error) = listen(callback) {\n                println!(\"监听事件时出错: {:?}\", error);\n            }\n        });\n    }\n\n    pub fn stop_recording(&mut self) {\n        let mut is_recording = self.is_recording.lock().unwrap();\n        *is_recording = false;\n    }\n\n    pub fn get_script(&self) -> String {\n        let actions = self.recorded_actions.lock().unwrap();\n        match serde_json::to_string_pretty(&*actions) {\n            Ok(json) => json,\n            Err(_) => \"脚本生成失败\".to_string(),\n        }\n    }\n\n    pub fn execute_script(&mut self, script_content: &str) {\n        let actions: Result<Vec<AutomationAction>, _> = serde_json::from_str(script_content);\n\n        match actions {\n            Ok(actions) => {\n                thread::spawn(move || {\n                    let mut enigo = Enigo::new();\n\n                    for action in actions {\n                        match action {\n                            AutomationAction::MouseMove { x, y, delay } => {\n                                thread::sleep(Duration::from_millis(delay));\n                                enigo.mouse_move_to(x, y);\n                            }\n                            AutomationAction::MouseClick {\n                                button,\n                                x,\n                                y,\n                                delay,\n                            } => {\n                                thread::sleep(Duration::from_millis(delay));\n                                enigo.mouse_move_to(x, y);\n                                match button {\n                                    MouseButton::Left => {\n                                        enigo.mouse_click(enigo::MouseButton::Left)\n                                    }\n                                    MouseButton::Right => {\n                                        enigo.mouse_click(enigo::MouseButton::Right)\n                                    }\n                                    MouseButton::Middle => {\n                                        enigo.mouse_click(enigo::MouseButton::Middle)\n                                    }\n                                }\n                            }\n                            AutomationAction::KeyPress { key, delay } => {\n                                thread::sleep(Duration::from_millis(delay));\n                                // 简化的按键处理，实际应用中需要更完善的键值映射\n                                if key.len() == 1 {\n                                    enigo.key_sequence(&key);\n                                }\n                            }\n                            AutomationAction::KeyRelease { delay, .. } => {\n                                thread::sleep(Duration::from_millis(delay));\n                                // 按键释放处理\n                            }\n                            AutomationAction::Wait { duration } => {\n                                thread::sleep(Duration::from_millis(duration));\n                            }\n                        }\n                    }\n                });\n            }\n            Err(e) => {\n                println!(\"脚本解析失败: {:?}\", e);\n            }\n        }\n    }\n\n    pub fn save_script(&self, script_content: &str) {\n        let scripts_dir = \"scripts\";\n        if !Path::new(scripts_dir).exists() {\n            fs::create_dir_all(scripts_dir).unwrap_or_else(|e| {\n                println!(\"创建脚本目录失败: {:?}\", e);\n            });\n        }\n\n        let timestamp = chrono::Utc::now().format(\"%Y%m%d_%H%M%S\").to_string();\n        let filename = format!(\"{}/script_{}.json\", scripts_dir, timestamp);\n\n        match fs::write(&filename, script_content) {\n            Ok(_) => println!(\"脚本已保存到: {}\", filename),\n            Err(e) => println!(\"保存脚本失败: {:?}\", e),\n        }\n    }\n\n    pub fn load_script(&self) -> Option<String> {\n        let scripts_dir = \"scripts\";\n        if !Path::new(scripts_dir).exists() {\n            return None;\n        }\n\n        // 获取最新的脚本文件\n        let entries = fs::read_dir(scripts_dir).ok()?;\n        let mut script_files: Vec<_> = entries\n            .filter_map(|entry| {\n                let entry = entry.ok()?;\n                let path = entry.path();\n                if path.extension()? == \"json\" {\n                    Some(path)\n                } else {\n                    None\n                }\n            })\n            .collect();\n\n        script_files.sort_by(|a, b| {\n            let a_metadata = fs::metadata(a).ok();\n            let b_metadata = fs::metadata(b).ok();\n            match (a_metadata, b_metadata) {\n                (Some(a_meta), Some(b_meta)) => b_meta\n                    .modified()\n                    .unwrap_or(std::time::UNIX_EPOCH)\n                    .cmp(&a_meta.modified().unwrap_or(std::time::UNIX_EPOCH)),\n                _ => std::cmp::Ordering::Equal,\n            }\n        });\n\n        if let Some(latest_script) = script_files.first() {\n            fs::read_to_string(latest_script).ok()\n        } else {\n            None\n        }\n    }\n}\n"}]}