{"sourceFile": "src/playback_control.rs", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1747967126772, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747967415805, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,198 @@\n+use crate::script_generator::load_script;\n+use enigo::{Enigo, Key, KeyboardControllable, MouseButton, MouseControllable};\n+use std::path::PathBuf;\n+use std::thread;\n+use std::time::Duration;\n+\n+pub fn playback_script(script_path: PathBuf, speed_factor: f64) -> Result<(), String> {\n+    println!(\n+        \"开始回放脚本: {:?}，速度因子: {}\",\n+        script_path, speed_factor\n+    );\n+\n+    // 加载脚本\n+    let script = match load_script(&script_path) {\n+        Ok(script) => script,\n+        Err(e) => {\n+            return Err(format!(\"加载脚本失败: {}\", e));\n+        }\n+    };\n+\n+    println!(\n+        \"脚本加载成功，共 {} 个事件，准备回放...\",\n+        script.events.len()\n+    );\n+    println!(\"回放将在3秒后开始，请将鼠标移动到目标位置...\");\n+\n+    // 倒计时\n+    for i in (1..=3).rev() {\n+        println!(\"{}\", i);\n+        thread::sleep(Duration::from_secs(1));\n+    }\n+\n+    println!(\"开始回放！\");\n+\n+    // 创建Enigo实例用于模拟输入\n+    let mut enigo = Enigo::new();\n+\n+    // 记录上一个事件的时间戳，用于计算延迟\n+    let mut last_timestamp: u128 = 0;\n+\n+    // 遍历并执行每个事件\n+    for (i, event) in script.events.iter().enumerate() {\n+        // 计算需要等待的时间\n+        let wait_time = if i == 0 {\n+            0 // 第一个事件不等待\n+        } else {\n+            let delta = event.timestamp - last_timestamp;\n+            (delta as f64 / speed_factor) as u64 // 根据速度因子调整等待时间\n+        };\n+\n+        // 等待指定时间\n+        if wait_time > 0 {\n+            thread::sleep(Duration::from_millis(wait_time));\n+        }\n+\n+        // 执行事件\n+        match (event.event_type.as_str(), event.action.as_str()) {\n+            (\"Keyboard\", \"Press\") => {\n+                if let Some(key) = parse_key(&event.detail) {\n+                    enigo.key_down(key);\n+                }\n+            }\n+            (\"Keyboard\", \"Release\") => {\n+                if let Some(key) = parse_key(&event.detail) {\n+                    enigo.key_up(key);\n+                }\n+            }\n+            (\"Mouse\", \"Press\") => {\n+                if let Some(button) = parse_mouse_button(&event.detail) {\n+                    enigo.mouse_down(button);\n+                }\n+            }\n+            (\"Mouse\", \"Release\") => {\n+                if let Some(button) = parse_mouse_button(&event.detail) {\n+                    enigo.mouse_up(button);\n+                }\n+            }\n+            (\"Mouse\", \"Move\") => {\n+                if let (Some(x), Some(y)) = (event.x, event.y) {\n+                    enigo.mouse_move_to(x as i32, y as i32);\n+                }\n+            }\n+            (\"Mouse\", \"Wheel\") => {\n+                // 解析滚轮事件的delta值\n+                if event.detail.contains(\"delta_y\") {\n+                    let parts: Vec<&str> = event.detail.split(',').collect();\n+                    if parts.len() >= 2 {\n+                        let delta_y_part = parts[1].trim();\n+                        if let Some(value_str) = delta_y_part.strip_prefix(\"delta_y: \") {\n+                            if let Ok(delta_y) = value_str.parse::<i32>() {\n+                                enigo.mouse_scroll_y(delta_y);\n+                            }\n+                        }\n+                    }\n+                }\n+            }\n+            _ => {\n+                println!(\"未知事件类型: {} {}\", event.event_type, event.action);\n+            }\n+        }\n+\n+        // 更新上一个事件的时间戳\n+        last_timestamp = event.timestamp;\n+    }\n+\n+    println!(\"脚本回放完成！\");\n+    Ok(())\n+}\n+\n+/// 将字符串解析为Enigo的Key枚举\n+fn parse_key(key_str: &str) -> Option<Key> {\n+    match key_str {\n+        \"KeyA\" => Some(Key::Layout('a')),\n+        \"KeyB\" => Some(Key::Layout('b')),\n+        \"KeyC\" => Some(Key::Layout('c')),\n+        \"KeyD\" => Some(Key::Layout('d')),\n+        \"KeyE\" => Some(Key::Layout('e')),\n+        \"KeyF\" => Some(Key::Layout('f')),\n+        \"KeyG\" => Some(Key::Layout('g')),\n+        \"KeyH\" => Some(Key::Layout('h')),\n+        \"KeyI\" => Some(Key::Layout('i')),\n+        \"KeyJ\" => Some(Key::Layout('j')),\n+        \"KeyK\" => Some(Key::Layout('k')),\n+        \"KeyL\" => Some(Key::Layout('l')),\n+        \"KeyM\" => Some(Key::Layout('m')),\n+        \"KeyN\" => Some(Key::Layout('n')),\n+        \"KeyO\" => Some(Key::Layout('o')),\n+        \"KeyP\" => Some(Key::Layout('p')),\n+        \"KeyQ\" => Some(Key::Layout('q')),\n+        \"KeyR\" => Some(Key::Layout('r')),\n+        \"KeyS\" => Some(Key::Layout('s')),\n+        \"KeyT\" => Some(Key::Layout('t')),\n+        \"KeyU\" => Some(Key::Layout('u')),\n+        \"KeyV\" => Some(Key::Layout('v')),\n+        \"KeyW\" => Some(Key::Layout('w')),\n+        \"KeyX\" => Some(Key::Layout('x')),\n+        \"KeyY\" => Some(Key::Layout('y')),\n+        \"KeyZ\" => Some(Key::Layout('z')),\n+        \"Digit0\" => Some(Key::Layout('0')),\n+        \"Digit1\" => Some(Key::Layout('1')),\n+        \"Digit2\" => Some(Key::Layout('2')),\n+        \"Digit3\" => Some(Key::Layout('3')),\n+        \"Digit4\" => Some(Key::Layout('4')),\n+        \"Digit5\" => Some(Key::Layout('5')),\n+        \"Digit6\" => Some(Key::Layout('6')),\n+        \"Digit7\" => Some(Key::Layout('7')),\n+        \"Digit8\" => Some(Key::Layout('8')),\n+        \"Digit9\" => Some(Key::Layout('9')),\n+        \"Space\" => Some(Key::Space),\n+        \"Return\" => Some(Key::Return),\n+        \"Tab\" => Some(Key::Tab),\n+        \"Escape\" => Some(Key::Escape),\n+        \"Control\" => Some(Key::Control),\n+        \"Alt\" => Some(Key::Alt),\n+        \"Shift\" => Some(Key::Shift),\n+        \"CapsLock\" => Some(Key::CapsLock),\n+        \"Meta\" => Some(Key::Meta),\n+        \"F1\" => Some(Key::F1),\n+        \"F2\" => Some(Key::F2),\n+        \"F3\" => Some(Key::F3),\n+        \"F4\" => Some(Key::F4),\n+        \"F5\" => Some(Key::F5),\n+        \"F6\" => Some(Key::F6),\n+        \"F7\" => Some(Key::F7),\n+        \"F8\" => Some(Key::F8),\n+        \"F9\" => Some(Key::F9),\n+        \"F10\" => Some(Key::F10),\n+        \"F11\" => Some(Key::F11),\n+        \"F12\" => Some(Key::F12),\n+        \"Backspace\" => Some(Key::Backspace),\n+        \"Delete\" => Some(Key::Delete),\n+        \"DownArrow\" => Some(Key::DownArrow),\n+        \"UpArrow\" => Some(Key::UpArrow),\n+        \"LeftArrow\" => Some(Key::LeftArrow),\n+        \"RightArrow\" => Some(Key::RightArrow),\n+        \"Home\" => Some(Key::Home),\n+        \"End\" => Some(Key::End),\n+        \"PageUp\" => Some(Key::PageUp),\n+        \"PageDown\" => Some(Key::PageDown),\n+        _ => {\n+            println!(\"未知键: {}\", key_str);\n+            None\n+        }\n+    }\n+}\n+\n+/// 将字符串解析为Enigo的MouseButton枚举\n+fn parse_mouse_button(button_str: &str) -> Option<MouseButton> {\n+    match button_str {\n+        \"Left\" => Some(MouseButton::Left),\n+        \"Right\" => Some(MouseButton::Right),\n+        \"Middle\" => Some(MouseButton::Middle),\n+        _ => {\n+            println!(\"未知鼠标按钮: {}\", button_str);\n+            None\n+        }\n+    }\n+}\n"}, {"date": 1747974984124, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -227,5 +227,5 @@\n             PlaybackAction::Stop => PlaybackAction::Stop,\n             PlaybackAction::Pause => PlaybackAction::Pause,\n         }\n     }\n-}\n+}\n\\ No newline at end of file\n"}], "date": 1747967126772, "name": "Commit-0", "content": "use crate::script_generator::load_script;\nuse enigo::{Enigo, Key, KeyboardControllable, MouseButton, MouseControllable};\nuse std::path::PathBuf;\nuse std::thread;\nuse std::time::Duration;\n\npub fn start_playback(script_path: &PathBuf, speed_factor: f64) {\n    println!(\n        \"开始回放脚本: {:?}，速度因子: {}\",\n        script_path, speed_factor\n    );\n\n    // 加载脚本\n    let script = match load_script(script_path) {\n        Ok(script) => script,\n        Err(e) => {\n            eprintln!(\"加载脚本失败: {}\", e);\n            return;\n        }\n    };\n\n    println!(\n        \"脚本加载成功，共 {} 个事件，准备回放...\",\n        script.events.len()\n    );\n    println!(\"回放将在3秒后开始，请将鼠标移动到目标位置...\");\n\n    // 倒计时\n    for i in (1..=3).rev() {\n        println!(\"{}\", i);\n        thread::sleep(Duration::from_secs(1));\n    }\n\n    println!(\"开始回放！\");\n\n    // 创建Enigo实例用于模拟输入\n    let mut enigo = Enigo::new();\n\n    // 记录上一个事件的时间戳，用于计算延迟\n    let mut last_timestamp: u128 = 0;\n\n    // 遍历并执行每个事件\n    for (i, event) in script.events.iter().enumerate() {\n        // 计算需要等待的时间\n        let wait_time = if i == 0 {\n            0 // 第一个事件不等待\n        } else {\n            let delta = event.timestamp - last_timestamp;\n            (delta as f64 / speed_factor) as u64 // 根据速度因子调整等待时间\n        };\n\n        // 等待指定时间\n        if wait_time > 0 {\n            thread::sleep(Duration::from_millis(wait_time));\n        }\n\n        // 执行事件\n        match (event.event_type.as_str(), event.action.as_str()) {\n            (\"Keyboard\", \"Press\") => {\n                if let Some(key) = parse_key(&event.detail) {\n                    enigo.key_down(key);\n                }\n            }\n            (\"Keyboard\", \"Release\") => {\n                if let Some(key) = parse_key(&event.detail) {\n                    enigo.key_up(key);\n                }\n            }\n            (\"Mouse\", \"Press\") => {\n                if let Some(button) = parse_mouse_button(&event.detail) {\n                    enigo.mouse_down(button);\n                }\n            }\n            (\"Mouse\", \"Release\") => {\n                if let Some(button) = parse_mouse_button(&event.detail) {\n                    enigo.mouse_up(button);\n                }\n            }\n            (\"Mouse\", \"Move\") => {\n                if let (Some(x), Some(y)) = (event.x, event.y) {\n                    enigo.mouse_move_to(x as i32, y as i32);\n                }\n            }\n            (\"Mouse\", \"Wheel\") => {\n                // 解析滚轮事件的delta值\n                if event.detail.contains(\"delta_y\") {\n                    let parts: Vec<&str> = event.detail.split(',').collect();\n                    if parts.len() >= 2 {\n                        let delta_y_part = parts[1].trim();\n                        if let Some(value_str) = delta_y_part.strip_prefix(\"delta_y: \") {\n                            if let Ok(delta_y) = value_str.parse::<i32>() {\n                                enigo.mouse_scroll_y(delta_y);\n                            }\n                        }\n                    }\n                }\n            }\n            _ => {\n                println!(\"未知事件类型: {} {}\", event.event_type, event.action);\n            }\n        }\n\n        // 更新上一个事件的时间戳\n        last_timestamp = event.timestamp;\n    }\n\n    println!(\"脚本回放完成！\");\n}\n\n/// 将字符串解析为Enigo的Key枚举\nfn parse_key(key_str: &str) -> Option<Key> {\n    match key_str {\n        \"KeyA\" => Some(Key::Layout('a')),\n        \"KeyB\" => Some(Key::Layout('b')),\n        \"KeyC\" => Some(Key::Layout('c')),\n        \"KeyD\" => Some(Key::Layout('d')),\n        \"KeyE\" => Some(Key::Layout('e')),\n        \"KeyF\" => Some(Key::Layout('f')),\n        \"KeyG\" => Some(Key::Layout('g')),\n        \"KeyH\" => Some(Key::Layout('h')),\n        \"KeyI\" => Some(Key::Layout('i')),\n        \"KeyJ\" => Some(Key::Layout('j')),\n        \"KeyK\" => Some(Key::Layout('k')),\n        \"KeyL\" => Some(Key::Layout('l')),\n        \"KeyM\" => Some(Key::Layout('m')),\n        \"KeyN\" => Some(Key::Layout('n')),\n        \"KeyO\" => Some(Key::Layout('o')),\n        \"KeyP\" => Some(Key::Layout('p')),\n        \"KeyQ\" => Some(Key::Layout('q')),\n        \"KeyR\" => Some(Key::Layout('r')),\n        \"KeyS\" => Some(Key::Layout('s')),\n        \"KeyT\" => Some(Key::Layout('t')),\n        \"KeyU\" => Some(Key::Layout('u')),\n        \"KeyV\" => Some(Key::Layout('v')),\n        \"KeyW\" => Some(Key::Layout('w')),\n        \"KeyX\" => Some(Key::Layout('x')),\n        \"KeyY\" => Some(Key::Layout('y')),\n        \"KeyZ\" => Some(Key::Layout('z')),\n        \"Digit0\" => Some(Key::Layout('0')),\n        \"Digit1\" => Some(Key::Layout('1')),\n        \"Digit2\" => Some(Key::Layout('2')),\n        \"Digit3\" => Some(Key::Layout('3')),\n        \"Digit4\" => Some(Key::Layout('4')),\n        \"Digit5\" => Some(Key::Layout('5')),\n        \"Digit6\" => Some(Key::Layout('6')),\n        \"Digit7\" => Some(Key::Layout('7')),\n        \"Digit8\" => Some(Key::Layout('8')),\n        \"Digit9\" => Some(Key::Layout('9')),\n        \"Space\" => Some(Key::Space),\n        \"Return\" => Some(Key::Return),\n        \"Tab\" => Some(Key::Tab),\n        \"Escape\" => Some(Key::Escape),\n        \"Control\" => Some(Key::Control),\n        \"Alt\" => Some(Key::Alt),\n        \"Shift\" => Some(Key::Shift),\n        \"CapsLock\" => Some(Key::CapsLock),\n        \"Meta\" => Some(Key::Meta),\n        \"F1\" => Some(Key::F1),\n        \"F2\" => Some(Key::F2),\n        \"F3\" => Some(Key::F3),\n        \"F4\" => Some(Key::F4),\n        \"F5\" => Some(Key::F5),\n        \"F6\" => Some(Key::F6),\n        \"F7\" => Some(Key::F7),\n        \"F8\" => Some(Key::F8),\n        \"F9\" => Some(Key::F9),\n        \"F10\" => Some(Key::F10),\n        \"F11\" => Some(Key::F11),\n        \"F12\" => Some(Key::F12),\n        \"Backspace\" => Some(Key::Backspace),\n        \"Delete\" => Some(Key::Delete),\n        \"DownArrow\" => Some(Key::DownArrow),\n        \"UpArrow\" => Some(Key::UpArrow),\n        \"LeftArrow\" => Some(Key::LeftArrow),\n        \"RightArrow\" => Some(Key::RightArrow),\n        \"Home\" => Some(Key::Home),\n        \"End\" => Some(Key::End),\n        \"PageUp\" => Some(Key::PageUp),\n        \"PageDown\" => Some(Key::PageDown),\n        _ => {\n            println!(\"未知键: {}\", key_str);\n            None\n        }\n    }\n}\n\n/// 将字符串解析为Enigo的MouseButton枚举\nfn parse_mouse_button(button_str: &str) -> Option<MouseButton> {\n    match button_str {\n        \"Left\" => Some(MouseButton::Left),\n        \"Right\" => Some(MouseButton::Right),\n        \"Middle\" => Some(MouseButton::Middle),\n        _ => {\n            println!(\"未知鼠标按钮: {}\", button_str);\n            None\n        }\n    }\n}\n"}]}